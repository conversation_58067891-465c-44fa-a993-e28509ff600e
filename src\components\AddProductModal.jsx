import React, { useState, useRef, useEffect } from 'react';
import { X, Upload, Camera, Plus } from 'lucide-react';
import { productCategories } from '../data/products';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';
import { useData } from '../contexts/DataContext';

const AddProductModal = ({ onClose, onSubmit }) => {
  const { t, tc } = useLanguage();
  const {
    addCustomCategory,
    addCustomSubcategory,
    getAllCategories,
    ensureCategoryExists,
    customProducts,
    updateCustomProduct
  } = useSettings();

  // Enhanced data storage context
  const {
    saveProductToDatabase,
    sqliteEnabled,
    saveApplicationData
  } = useData();

  // Form state
  const [formData, setFormData] = useState({
    name: '',
    category: '',
    subcategory: '',
    price: '0.00',
    quantity: '1',
    diameter: 'N/A',
    material: 'Standard',
    image: null
  });

  // UI state
  const [errors, setErrors] = useState({});
  const [imagePreview, setImagePreview] = useState(null);
  const [showAddCategory, setShowAddCategory] = useState(false);
  const [showAddSubcategory, setShowAddSubcategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newSubcategoryName, setNewSubcategoryName] = useState('');
  const [newSubcategoryParent, setNewSubcategoryParent] = useState('');
  const [categoryErrors, setCategoryErrors] = useState({});

  // Duplicate handling state
  const [showDuplicateDialog, setShowDuplicateDialog] = useState(false);
  const [duplicateProduct, setDuplicateProduct] = useState(null);
  const [pendingProductData, setPendingProductData] = useState(null);

  // Camera functionality state
  const [showCameraStream, setShowCameraStream] = useState(false);
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [showCameraReadyIndicator, setShowCameraReadyIndicator] = useState(false);
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  // URL image loading state
  const [urlInput, setUrlInput] = useState('');
  const [isLoadingUrl, setIsLoadingUrl] = useState(false);
  const [urlValidationStatus, setUrlValidationStatus] = useState(''); // 'valid', 'invalid', 'loading', ''
  const urlTimeoutRef = useRef(null);

  // Cleanup camera stream on unmount
  useEffect(() => {
    return () => {
      if (streamRef.current) {
        streamRef.current.getTracks().forEach(track => track.stop());
      }
      if (urlTimeoutRef.current) {
        clearTimeout(urlTimeoutRef.current);
      }
    };
  }, []);

  // Input change handler
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
    
    // Clear field-specific errors when user starts typing
    if (errors[name]) {
      setErrors(prev => ({ ...prev, [name]: '' }));
    }
  };

  // Image upload handler
  const handleImageUpload = (e) => {
    const file = e.target.files[0];
    if (file) {
      if (file.type.startsWith('image/')) {
        // Validate file size (max 5MB for better performance)
        if (file.size > 5 * 1024 * 1024) {
          setErrors(prev => ({
            ...prev,
            image: t('imageTooLarge') || 'Image file is too large (max 5MB)'
          }));
          return;
        }

        // Validate file type more strictly
        const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(file.type)) {
          setErrors(prev => ({
            ...prev,
            image: t('unsupportedImageFormat') || 'Unsupported image format. Please use JPG, PNG, GIF, or WebP.'
          }));
          return;
        }

        const reader = new FileReader();
        reader.onload = (e) => {
          const base64Data = e.target.result;
          
          // Validate base64 data
          if (!base64Data || base64Data === 'data:,' || base64Data.length < 100) {
            setErrors(prev => ({
              ...prev,
              image: t('invalidImageFile') || 'Invalid image file. Please try another image.'
            }));
            return;
          }

          setFormData(prev => ({ ...prev, image: base64Data }));
          setImagePreview(base64Data);
          setErrors(prev => ({ ...prev, image: '' }));
        };
        reader.onerror = () => {
          setErrors(prev => ({
            ...prev,
            image: t('imageReadError') || 'Error reading image file. Please try again.'
          }));
        };
        reader.readAsDataURL(file);
      } else {
        setErrors(prev => ({
          ...prev,
          image: t('invalidFileType') || 'Please select a valid image file'
        }));
      }
    }
  };

  // URL validation helper
  const validateImageUrl = (url) => {
    if (!url) return false;
    
    // Check for data URLs
    if (url.startsWith('data:image/')) return true;
    
    // Check for valid URL format
    try {
      const urlObj = new URL(url.startsWith('http') ? url : `https://${url}`);
      const validDomains = ['imgur.com', 'i.imgur.com', 'github.com', 'githubusercontent.com', 'unsplash.com', 'pexels.com'];
      const hasValidExtension = /\.(jpg|jpeg|png|gif|webp|svg)(\?.*)?$/i.test(urlObj.pathname);
      const isValidDomain = validDomains.some(domain => urlObj.hostname.includes(domain));
      
      return hasValidExtension || isValidDomain;
    } catch {
      return false;
    }
  };

  // Real-time URL validation with debouncing
  const handleUrlInputChange = (value) => {
    setUrlInput(value);

    // Clear previous timeout
    if (urlTimeoutRef.current) {
      clearTimeout(urlTimeoutRef.current);
    }

    // Reset validation status
    setUrlValidationStatus('');
    setErrors(prev => ({ ...prev, image: '' }));

    if (!value.trim()) {
      return;
    }

    // Debounce validation
    urlTimeoutRef.current = setTimeout(() => {
      const trimmedUrl = value.trim();

      if (validateImageUrl(trimmedUrl)) {
        setUrlValidationStatus('valid');
      } else {
        setUrlValidationStatus('invalid');
        setErrors(prev => ({
          ...prev,
          image: t('invalidImageUrl') || 'Please enter a valid image URL (e.g., https://example.com/image.jpg)'
        }));
      }
    }, 500); // 500ms debounce
  };

  // Handle image URL
  const handleImageUrl = (url) => {
    const trimmedUrl = url.trim();
    if (!trimmedUrl) return;

    // Clear URL input
    setUrlInput('');
    setUrlValidationStatus('');

    // Validate URL format
    if (!validateImageUrl(trimmedUrl)) {
      setErrors(prev => ({
        ...prev,
        image: t('invalidImageUrl') || 'Please enter a valid image URL (e.g., https://example.com/image.jpg)'
      }));
      return;
    }

    // Ensure URL has protocol for external URLs
    let finalUrl = trimmedUrl;
    const isDataUrl = trimmedUrl.startsWith('data:image/');

    if (!isDataUrl && !trimmedUrl.startsWith('http://') && !trimmedUrl.startsWith('https://')) {
      finalUrl = 'https://' + trimmedUrl;
    }

    // Set loading state
    setIsLoadingUrl(true);
    setErrors(prev => ({ ...prev, image: '' }));

    // Create a timeout for the image loading
    const timeout = setTimeout(() => {
      setIsLoadingUrl(false);
      setErrors(prev => ({
        ...prev,
        image: t('imageLoadTimeout') || 'Image loading timed out. Please check the URL and try again.'
      }));
    }, 10000); // 10 second timeout

    // Test if the image can be loaded
    const testImage = new Image();
    testImage.crossOrigin = 'anonymous';

    testImage.onload = () => {
      clearTimeout(timeout);
      setIsLoadingUrl(false);

      // Validate image dimensions
      if (testImage.width === 0 || testImage.height === 0) {
        setErrors(prev => ({
          ...prev,
          image: t('invalidImageFile') || 'Invalid image file. Please check the URL.'
        }));
        return;
      }

      // Validate reasonable image size (not too large)
      if (testImage.width > 5000 || testImage.height > 5000) {
        setErrors(prev => ({
          ...prev,
          image: t('imageTooLarge') || 'Image is too large. Please use an image smaller than 5000x5000 pixels.'
        }));
        return;
      }

      // Success - set the image
      setFormData(prev => ({
        ...prev,
        image: finalUrl
      }));
      setImagePreview(finalUrl);
      setErrors(prev => ({
        ...prev,
        image: ''
      }));
    };

    testImage.onerror = () => {
      clearTimeout(timeout);
      setIsLoadingUrl(false);
      setErrors(prev => ({
        ...prev,
        image: t('imageLoadError') || 'Failed to load image from URL. Please check the URL and try again.'
      }));
    };

    // Start loading the image
    testImage.src = finalUrl;
  };

  // Find duplicate product
  const findDuplicateProduct = (name, category, subcategory) => {
    return customProducts.find(product => 
      product.name.toLowerCase() === name.toLowerCase() &&
      product.category.toLowerCase() === category.toLowerCase() &&
      product.subcategory.toLowerCase() === subcategory.toLowerCase()
    );
  };

  // Form validation
  const validateForm = () => {
    const newErrors = {};

    // Only validate required fields
    if (!formData.name.trim()) newErrors.name = t('productNameRequired');
    if (!formData.category) newErrors.category = t('categoryRequired');
    if (!formData.subcategory) newErrors.subcategory = t('subcategoryRequired');

    // Validate quantity
    const quantity = parseInt(formData.quantity);
    if (!formData.quantity || isNaN(quantity) || quantity < 1) {
      newErrors.quantity = t('validQuantityRequired') || 'Valid quantity (minimum 1) is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle duplicate resolution
  const handleDuplicateResolution = async (action) => {
    if (!pendingProductData || !duplicateProduct) return;

    switch (action) {
      case 'replace':
        try {
          const updatedProductData = {
            ...duplicateProduct,
            ...pendingProductData,
            updatedAt: new Date().toISOString()
          };

          // Save to enhanced database storage first
          if (saveProductToDatabase) {
            console.log('Updating product in enhanced database storage...');
            const dbResult = await saveProductToDatabase(updatedProductData);

            if (dbResult.success) {
              console.log('Product updated in database successfully:', dbResult);
            } else {
              console.warn('Database update failed, falling back to localStorage:', dbResult.message);
            }
          }

          // Update the existing product with new data
          updateCustomProduct(duplicateProduct.id, updatedProductData);

          // Trigger application data save to ensure consistency
          if (saveApplicationData) {
            try {
              await saveApplicationData();
              console.log('Application data synchronized after product update');
            } catch (saveError) {
              console.warn('Failed to synchronize application data after update:', saveError);
            }
          }

          // Show success message and close modal
          alert(t('Product replaced successfully!') || 'Product replaced successfully!');
          onClose();
        } catch (error) {
          console.error('Error updating product:', error);
          alert(t('Failed to update product. Please try again.') || 'Failed to update product. Please try again.');
        }
        break;

      case 'modify':
        // Keep the form open for user to modify
        setShowDuplicateDialog(false);
        setDuplicateProduct(null);
        setPendingProductData(null);
        // Form stays open with current data
        break;

      case 'cancel':
        // Cancel the operation
        setShowDuplicateDialog(false);
        setDuplicateProduct(null);
        setPendingProductData(null);
        break;

      default:
        break;
    }
  };

  // Category management functions
  const handleAddCategory = () => {
    const trimmedName = newCategoryName.trim();
    if (!trimmedName) {
      setCategoryErrors({ category: t('categoryNameRequired') });
      return;
    }

    const allCategories = getAllCategories();
    const existingCategory = allCategories.find(cat =>
      cat.name.toLowerCase() === trimmedName.toLowerCase()
    );

    if (existingCategory) {
      setCategoryErrors({ category: t('categoryNameExists') });
      return;
    }

    addCustomCategory(trimmedName);
    setFormData(prev => ({ ...prev, category: trimmedName, subcategory: '' }));
    setNewCategoryName('');
    setShowAddCategory(false);
    setCategoryErrors({});

    // Show success message (you could also pass this up to parent component)
    console.log(t('categoryCreatedSuccessfully') || 'Category created successfully');
  };

  const handleAddSubcategory = () => {
    const trimmedName = newSubcategoryName.trim();
    const trimmedParent = newSubcategoryParent.trim();

    if (!trimmedName) {
      setCategoryErrors({ subcategory: t('subcategoryNameRequired') });
      return;
    }

    if (!trimmedParent) {
      setCategoryErrors({ parent: t('parentCategoryRequired') });
      return;
    }

    const allCategories = getAllCategories();
    const parentCategory = allCategories.find(cat => cat.name === trimmedParent);

    if (parentCategory && parentCategory.subcategories.includes(trimmedName)) {
      setCategoryErrors({ subcategory: t('subcategoryNameExists') });
      return;
    }

    addCustomSubcategory(trimmedParent, trimmedName);
    setFormData(prev => ({
      ...prev,
      category: trimmedParent,
      subcategory: trimmedName
    }));
    setNewSubcategoryName('');
    setNewSubcategoryParent('');
    setShowAddSubcategory(false);
    setCategoryErrors({});

    // Show success message (you could also pass this up to parent component)
    console.log(t('subcategoryCreatedSuccessfully') || 'Subcategory created successfully');
  };

  // Main form submission handler
  const handleSubmit = async (e) => {
    e.preventDefault();

    if (validateForm()) {
      // Simple placeholder image for better compatibility
      const simplePlaceholder = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADwAAAA8CAYAAAA6/NlyAAAACXBIWXMAAAsTAAALEwEAmpwYAAABaUlEQVR4nO2ZQU7DMBBFXwVLWkcsuuAQ5VBcAg7DnlP0ArRCUJZVd43VLmpIadLSJk7nw8hSpKjJ/PmbsT2WCSGEEEIIIcT/xMzOgAfgE1hHjUPHQQwzuwQWUeM99gOsXG+0ALvAMgr4rQXuADaI8GoL8bDYB+DEzGbAGZDHnwSrZgTYoKE1t0+Mq2YE2MxugPdU1cwWsIXf85zx4RXn3H13DQ36HA7xeX7n95zzK+p0DJoSXgA3hYuGBmXCOXe9efQU8kDGK78GHmP7GrQ5TGpwfz4wKOElcJuq2dB+QnnlIcDKGCcsYYHzyk9Q0LS1qKrZgQrLK78F1r4HDWjO7wZ4SdVszz9mD3teeRNY4By4T9Us1qBlUVUGHaMdqLDyyntgfagfOsYWVJ6YYUm4BgzxOet55edUzVJwrI6hyjjhfSisZx/+yjGEbMJZTICvHHMb4E/3wpTxDHxLWAghhBBCCCEO4xu8YT7UwwFdnwAAAABJRU5ErkJggg==';

      // Create a default placeholder image if none provided
      const defaultImage = formData.image || simplePlaceholder;

      // Validate that the image is a valid data URL with extra safety
      const isValidImage = typeof defaultImage === 'string' &&
        (defaultImage.startsWith('data:image/') || defaultImage.startsWith('http'));

      // Use valid image or fallback to a simple placeholder
      const fallbackImage = simplePlaceholder;
      const finalImage = isValidImage ? defaultImage : fallbackImage;

      // Prepare product data with proper category/subcategory association
      const productData = {
        ...formData,
        name: formData.name.trim(),
        price: parseFloat(formData.price) || 0,
        quantity: parseInt(formData.quantity) || 1,
        diameter: formData.diameter || 'N/A',
        material: formData.material || 'Standard',
        image: finalImage,
        // Ensure category and subcategory are properly set
        category: formData.category.trim(),
        subcategory: formData.subcategory.trim()
      };

      // Console log the image for debugging
      console.log('Product image data:', finalImage.substring(0, 50) + '...');

      // Check for duplicate products
      const duplicate = findDuplicateProduct(
        productData.name,
        productData.category,
        productData.subcategory
      );

      if (duplicate) {
        // Show duplicate dialog
        setDuplicateProduct(duplicate);
        setPendingProductData(productData);
        setShowDuplicateDialog(true);
        return;
      }

      // No duplicate found, proceed with normal submission
      // Check if we're creating new categories/subcategories
      const allCategories = getAllCategories();
      const existingCategory = allCategories.find(cat => cat.name === productData.category);
      const isNewCategory = !existingCategory;
      const isNewSubcategory = existingCategory && !existingCategory.subcategories.includes(productData.subcategory);

      try {
        // Save to enhanced database storage first
        if (saveProductToDatabase) {
          console.log('Saving product to enhanced database storage...');
          const dbResult = await saveProductToDatabase(productData);

          if (dbResult.success) {
            console.log('Product saved to database successfully:', dbResult);
          } else {
            console.warn('Database save failed, falling back to localStorage:', dbResult.message);
          }
        }

        // Also submit through the traditional flow (this will auto-create categories/subcategories if needed)
        onSubmit(productData);

        // Trigger application data save to ensure consistency
        if (saveApplicationData) {
          try {
            await saveApplicationData();
            console.log('Application data synchronized successfully');
          } catch (saveError) {
            console.warn('Failed to synchronize application data:', saveError);
          }
        }

        // Show appropriate success message
        let message = `Product "${productData.name}" added successfully!`;
        if (sqliteEnabled) {
          message += ' (Saved to database)';
        }
        if (isNewCategory) {
          message += ` New category "${productData.category}" created.`;
        } else if (isNewSubcategory) {
          message += ` New subcategory "${productData.subcategory}" added to "${productData.category}".`;
        }

        console.log(message);

        // Close the modal on successful save
        onClose();

      } catch (error) {
        console.error('Error saving product:', error);
        setErrors(prev => ({
          ...prev,
          general: t('Failed to save product. Please try again.') || 'Failed to save product. Please try again.'
        }));
      }
    }
  };

  // Camera functionality
  const startCameraStream = async () => {
    setIsProcessingImage(true);
    setIsCameraReady(false);
    setShowCameraReadyIndicator(false);

    try {
      const stream = await navigator.mediaDevices.getUserMedia({
        video: {
          width: { ideal: 1280 },
          height: { ideal: 720 },
          facingMode: 'environment' // Use back camera if available
        }
      });

      streamRef.current = stream;

      if (videoRef.current) {
        videoRef.current.srcObject = stream;

        // Wait for video to be ready
        videoRef.current.onloadedmetadata = () => {
          setIsProcessingImage(false);
          setShowCameraStream(true);

          // Small delay to ensure video is fully loaded
          setTimeout(() => {
            setIsCameraReady(true);
            setShowCameraReadyIndicator(true);

            // Hide ready indicator after 2 seconds
            setTimeout(() => {
              setShowCameraReadyIndicator(false);
            }, 2000);
          }, 500);
        };
      }
    } catch (error) {
      console.error('Error accessing camera:', error);
      setIsProcessingImage(false);
      setErrors(prev => ({
        ...prev,
        image: t('cameraAccessError') || 'Unable to access camera. Please check permissions or use file upload instead.'
      }));
    }
  };

  const stopCameraStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setShowCameraStream(false);
    setIsCameraReady(false);
    setIsProcessingImage(false);
    setShowCameraReadyIndicator(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !isCameraReady) {
      setErrors(prev => ({ ...prev, image: 'Camera not ready. Please wait a moment and try again.' }));
      return;
    }

    const video = videoRef.current;
    const canvas = canvasRef.current || document.createElement('canvas');
    const context = canvas.getContext('2d');

    if (!context) {
      setErrors(prev => ({ ...prev, image: 'Unable to capture photo. Please try again.' }));
      return;
    }

    try {
      // Get actual video dimensions
      const videoWidth = video.videoWidth || video.clientWidth || 640;
      const videoHeight = video.videoHeight || video.clientHeight || 480;

      // Set canvas dimensions to match video
      canvas.width = videoWidth;
      canvas.height = videoHeight;

      // Clear canvas first
      context.clearRect(0, 0, canvas.width, canvas.height);

      // Draw the video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to base64 with good quality
      const base64Data = canvas.toDataURL('image/jpeg', 0.85);

      // Validate the captured image
      if (base64Data === 'data:,' || base64Data.length < 1000) {
        setErrors(prev => ({ ...prev, image: 'Failed to capture photo. Please try again.' }));
        return;
      }

      // Success - set the captured image
      setFormData(prev => ({ ...prev, image: base64Data }));
      setImagePreview(base64Data);
      setErrors(prev => ({ ...prev, image: '' }));

      // Stop camera stream after successful capture
      stopCameraStream();

    } catch (error) {
      console.error('Error capturing photo:', error);
      setErrors(prev => ({ ...prev, image: 'Failed to capture photo. Please try again.' }));
    }
  };

  // Get all categories including custom ones
  const allCategories = getAllCategories();
  const selectedCategory = allCategories.find(cat => cat.name === formData.category);

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-md max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-dark-700">
          <h2 className="text-xl font-semibold text-white">{t('addNewProduct')}</h2>
          <button
            onClick={onClose}
            className="p-2 hover:bg-dark-700 rounded-lg transition-colors"
          >
            <X className="w-5 h-5" />
          </button>
        </div>

        {/* Form */}
        <form onSubmit={handleSubmit} className="p-6 space-y-4">
          {/* General Error Display */}
          {errors.general && (
            <div className="p-3 rounded-lg bg-red-900/20 border border-red-500/30 text-red-400">
              <p className="text-sm font-medium">{errors.general}</p>
            </div>
          )}

          {/* Product Name */}
          <div>
            <label className="block text-sm font-medium text-dark-200 mb-2">
              {t('productName')} *
            </label>
            <input
              type="text"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              placeholder={t('enterProductName')}
            />
            {errors.name && <p className="text-red-400 text-xs mt-1">{errors.name}</p>}
          </div>

          {/* Category */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-dark-200">
                {t('category')} *
              </label>
              <button
                type="button"
                onClick={() => setShowAddCategory(true)}
                className="flex items-center gap-1 px-2 py-1 text-xs bg-primary-600 hover:bg-primary-700 rounded text-white transition-colors"
              >
                <Plus className="w-3 h-3" />
                {t('addNewCategory')}
              </button>
            </div>
            <select
              name="category"
              value={formData.category}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
            >
              <option value="">{t('selectCategory')}</option>
              {allCategories.map(category => (
                <option key={category.name} value={category.name}>
                  {tc(category.name)}
                </option>
              ))}
            </select>
            {errors.category && <p className="text-red-400 text-xs mt-1">{errors.category}</p>}
          </div>

          {/* Subcategory */}
          <div>
            <div className="flex items-center justify-between mb-2">
              <label className="block text-sm font-medium text-dark-200">
                {t('subcategory')} *
              </label>
              <button
                type="button"
                onClick={() => {
                  setNewSubcategoryParent(formData.category);
                  setShowAddSubcategory(true);
                }}
                disabled={!formData.category}
                className="flex items-center gap-1 px-2 py-1 text-xs bg-primary-600 hover:bg-primary-700 rounded text-white transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Plus className="w-3 h-3" />
                {t('addNewSubcategory')}
              </button>
            </div>
            <select
              name="subcategory"
              value={formData.subcategory}
              onChange={handleInputChange}
              disabled={!selectedCategory}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500 disabled:opacity-50"
            >
              <option value="">{t('selectSubcategory')}</option>
              {selectedCategory?.subcategories.map(subcategory => (
                <option key={subcategory} value={subcategory}>
                  {subcategory}
                </option>
              ))}
            </select>
            {errors.subcategory && <p className="text-red-400 text-xs mt-1">{errors.subcategory}</p>}
          </div>

          {/* Price, Quantity, and Diameter */}
          <div className="grid grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('price')}
              </label>
              <input
                type="number"
                name="price"
                value={formData.price}
                onChange={handleInputChange}
                step="0.01"
                min="0"
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                placeholder="0.00"
              />
              <p className="text-dark-400 text-xs mt-1">{t('optional') || 'Optional'}</p>
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('quantity') || 'Quantity'} *
              </label>
              <input
                type="number"
                name="quantity"
                value={formData.quantity}
                onChange={handleInputChange}
                min="1"
                step="1"
                className={`w-full px-3 py-2 bg-dark-700 border rounded-lg text-white focus:outline-none transition-colors ${
                  errors.quantity
                    ? 'border-red-500 focus:border-red-400'
                    : 'border-dark-600 focus:border-primary-500'
                }`}
                placeholder={t('enterQuantity') || 'Enter quantity'}
              />
              {errors.quantity && <p className="text-red-400 text-xs mt-1">{errors.quantity}</p>}
            </div>

            <div>
              <label className="block text-sm font-medium text-dark-200 mb-2">
                {t('diameter')}
              </label>
              <input
                type="text"
                name="diameter"
                value={formData.diameter}
                onChange={handleInputChange}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                placeholder="N/A"
              />
              <p className="text-dark-400 text-xs mt-1">{t('optional') || 'Optional'}</p>
            </div>
          </div>

          {/* Material - Optional */}
          <div>
            <label className="block text-sm font-medium text-dark-200 mb-2">
              {t('material')}
            </label>
            <input
              type="text"
              name="material"
              value={formData.material}
              onChange={handleInputChange}
              className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
              placeholder="Standard"
            />
            <p className="text-dark-400 text-xs mt-1">{t('optional') || 'Optional'}</p>
          </div>

          {/* Image Upload - Optional */}
          <div>
            <label className="block text-sm font-medium text-dark-200 mb-2">
              {t('productImage')}
            </label>
            <p className="text-dark-400 text-xs mb-2">{t('optional') || 'Optional'} - {t('defaultImageWillBeUsed') || 'A default placeholder will be used if no image is provided'}</p>

            <div className="space-y-3">
              {/* Image Error Display */}
              {errors.image && (
                <div className="p-3 rounded-lg bg-red-900/20 border border-red-500/30 text-red-400">
                  <p className="text-sm font-medium">{errors.image}</p>
                </div>
              )}

              {/* Enhanced Image Preview */}
              {imagePreview && (
                <div className="relative group">
                  <div className="relative bg-gray-100 rounded-lg border border-dark-600 overflow-hidden">
                    <img
                      src={imagePreview}
                      alt="Preview"
                      className="w-full h-40 object-contain bg-gradient-to-br from-gray-50 to-gray-100"
                      onLoad={(e) => {
                        // Log successful image load
                        console.log('Image preview loaded successfully:', {
                          width: e.target.naturalWidth,
                          height: e.target.naturalHeight,
                          src: imagePreview.substring(0, 50) + '...'
                        });
                      }}
                      onError={(e) => {
                        console.error('Image preview failed to load:', imagePreview);
                        setErrors(prev => ({
                          ...prev,
                          image: 'Failed to display image preview. The image may be corrupted or inaccessible.'
                        }));
                        setImagePreview(null);
                        setFormData(prev => ({ ...prev, image: null }));
                      }}
                    />

                    {/* Image info overlay */}
                    <div className="absolute bottom-0 left-0 right-0 bg-black bg-opacity-70 text-white text-xs p-2 opacity-0 group-hover:opacity-100 transition-opacity">
                      <div className="flex justify-between items-center">
                        <span>Image loaded successfully</span>
                        <span className="text-green-300">✓</span>
                      </div>
                    </div>
                  </div>

                  {/* Remove button */}
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview(null);
                      setFormData(prev => ({ ...prev, image: null }));
                      setUrlInput('');
                      setUrlValidationStatus('');
                      setErrors(prev => ({ ...prev, image: '' }));
                    }}
                    className="absolute -top-2 -right-2 w-7 h-7 bg-red-500 hover:bg-red-600 rounded-full flex items-center justify-center text-white text-sm font-bold shadow-lg transition-colors"
                    title="Remove image"
                  >
                    ×
                  </button>
                </div>
              )}

              {/* Upload Options */}
              {!imagePreview && (
                <>
                  <label className="flex items-center justify-center w-full h-32 border-2 border-dashed border-dark-600 rounded-lg cursor-pointer hover:border-primary-500 transition-colors">
                    <div className="text-center">
                      <Upload className="w-8 h-8 mx-auto mb-2 text-dark-400" />
                      <p className="text-sm text-dark-400">{t('clickToUpload') || 'Click to upload'}</p>
                      <p className="text-xs text-dark-500 mt-1">{t('supportedFormats') || 'JPG, PNG, GIF up to 10MB'}</p>
                    </div>
                    <input
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                  </label>

                  {/* Enhanced Camera option */}
                  {!showCameraStream ? (
                    <button
                      type="button"
                      onClick={startCameraStream}
                      disabled={isProcessingImage}
                      className={`flex items-center justify-center w-full py-2 border border-dark-600 rounded-lg transition-colors ${
                        isProcessingImage
                          ? 'cursor-not-allowed opacity-50'
                          : 'cursor-pointer hover:border-primary-500'
                      }`}
                    >
                      <Camera className="w-4 h-4 mr-2" />
                      <span className="text-sm">
                        {isProcessingImage ? t('Starting camera...') || 'Starting camera...' : t('takePhoto') || 'Take Photo'}
                      </span>
                    </button>
                  ) : (
                    <div className="space-y-3">
                      <div className="relative bg-black rounded-lg overflow-hidden">
                        <video
                          ref={videoRef}
                          autoPlay
                          playsInline
                          className="w-full h-48 object-cover"
                        />
                        <div className="absolute bottom-3 left-1/2 transform -translate-x-1/2 flex space-x-3">
                          <button
                            type="button"
                            onClick={capturePhoto}
                            disabled={!isCameraReady}
                            className={`px-3 py-2 rounded-lg text-sm font-medium transition-colors ${
                              isCameraReady
                                ? 'bg-white text-gray-900 hover:bg-gray-100'
                                : 'bg-gray-400 text-gray-600 cursor-not-allowed'
                            }`}
                          >
                            <Camera size={14} className="inline mr-1" />
                            {isCameraReady ? t('Capture') || 'Capture' : t('Preparing...') || 'Preparing...'}
                          </button>
                          <button
                            type="button"
                            onClick={stopCameraStream}
                            className="px-3 py-2 bg-red-600 text-white rounded-lg text-sm font-medium hover:bg-red-700 transition-colors"
                          >
                            <X size={14} className="inline mr-1" />
                            {t('Cancel') || 'Cancel'}
                          </button>
                        </div>

                        {/* Camera ready indicator */}
                        {showCameraReadyIndicator && (
                          <div className="absolute top-3 left-3 bg-green-500 text-white px-2 py-1 rounded text-xs font-medium">
                            {t('Camera Ready') || 'Camera Ready'}
                          </div>
                        )}
                      </div>
                      <canvas ref={canvasRef} className="hidden" />
                    </div>
                  )}

                  {/* Enhanced URL Input */}
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <input
                        type="url"
                        value={urlInput}
                        onChange={(e) => handleUrlInputChange(e.target.value)}
                        placeholder={t('enterImageUrl') || 'Enter image URL (e.g., https://example.com/image.jpg)'}
                        className={`flex-1 px-3 py-2 bg-dark-700 border rounded-lg text-white focus:outline-none transition-colors ${
                          urlValidationStatus === 'invalid'
                            ? 'border-red-500 focus:border-red-400'
                            : urlValidationStatus === 'valid'
                            ? 'border-green-500 focus:border-green-400'
                            : 'border-dark-600 focus:border-primary-500'
                        }`}
                      />
                      <button
                        type="button"
                        onClick={() => handleImageUrl(urlInput)}
                        disabled={!urlInput.trim() || urlValidationStatus === 'invalid' || isLoadingUrl}
                        className="px-4 py-2 bg-primary-600 hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed rounded-lg text-white text-sm font-medium transition-colors"
                      >
                        {isLoadingUrl ? t('Loading...') || 'Loading...' : t('Load') || 'Load'}
                      </button>
                    </div>

                    {/* URL validation feedback */}
                    {urlValidationStatus === 'valid' && (
                      <p className="text-green-400 text-xs flex items-center">
                        <span className="w-2 h-2 bg-green-400 rounded-full mr-2"></span>
                        {t('Valid image URL') || 'Valid image URL'}
                      </p>
                    )}
                    {urlValidationStatus === 'invalid' && (
                      <p className="text-red-400 text-xs flex items-center">
                        <span className="w-2 h-2 bg-red-400 rounded-full mr-2"></span>
                        {t('Invalid URL format') || 'Invalid URL format'}
                      </p>
                    )}

                    <p className="text-dark-400 text-xs">
                      {t('urlImageHint') || 'Supported: Direct image links from imgur.com, github.com, unsplash.com, etc.'}
                    </p>
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Form Actions */}
          <div className="flex justify-end space-x-3 pt-4 border-t border-dark-700">
            <button
              type="button"
              onClick={onClose}
              className="px-4 py-2 text-dark-300 hover:text-white transition-colors"
            >
              {t('cancel')}
            </button>
            <button
              type="submit"
              className="px-6 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors"
            >
              {t('addProduct')}
            </button>
          </div>
        </form>
      </div>

      {/* Add Category Modal */}
      {showAddCategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-sm mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">{t('addNewCategory')}</h3>
              <input
                type="text"
                value={newCategoryName}
                onChange={(e) => setNewCategoryName(e.target.value)}
                placeholder={t('categoryName')}
                className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500 mb-2"
                onKeyPress={(e) => e.key === 'Enter' && handleAddCategory()}
              />
              {categoryErrors.category && (
                <p className="text-red-400 text-xs mb-3">{categoryErrors.category}</p>
              )}
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => {
                    setShowAddCategory(false);
                    setNewCategoryName('');
                    setCategoryErrors({});
                  }}
                  className="px-4 py-2 text-dark-300 hover:text-white transition-colors"
                >
                  {t('cancel')}
                </button>
                <button
                  onClick={handleAddCategory}
                  className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                  {t('add')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Add Subcategory Modal */}
      {showAddSubcategory && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-sm mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">{t('addNewSubcategory')}</h3>
              <div className="space-y-3">
                <div>
                  <label className="block text-sm font-medium text-dark-200 mb-1">
                    {t('parentCategory')}
                  </label>
                  <select
                    value={newSubcategoryParent}
                    onChange={(e) => setNewSubcategoryParent(e.target.value)}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                  >
                    <option value="">{t('selectCategory')}</option>
                    {allCategories.map(category => (
                      <option key={category.name} value={category.name}>
                        {tc(category.name)}
                      </option>
                    ))}
                  </select>
                  {categoryErrors.parent && (
                    <p className="text-red-400 text-xs mt-1">{categoryErrors.parent}</p>
                  )}
                </div>
                <div>
                  <label className="block text-sm font-medium text-dark-200 mb-1">
                    {t('subcategoryName')}
                  </label>
                  <input
                    type="text"
                    value={newSubcategoryName}
                    onChange={(e) => setNewSubcategoryName(e.target.value)}
                    placeholder={t('subcategoryName')}
                    className="w-full px-3 py-2 bg-dark-700 border border-dark-600 rounded-lg text-white focus:outline-none focus:border-primary-500"
                    onKeyPress={(e) => e.key === 'Enter' && handleAddSubcategory()}
                  />
                  {categoryErrors.subcategory && (
                    <p className="text-red-400 text-xs mt-1">{categoryErrors.subcategory}</p>
                  )}
                </div>
              </div>
              <div className="flex justify-end space-x-3 mt-4">
                <button
                  onClick={() => {
                    setShowAddSubcategory(false);
                    setNewSubcategoryName('');
                    setNewSubcategoryParent('');
                    setCategoryErrors({});
                  }}
                  className="px-4 py-2 text-dark-300 hover:text-white transition-colors"
                >
                  {t('cancel')}
                </button>
                <button
                  onClick={handleAddSubcategory}
                  className="px-4 py-2 bg-primary-600 hover:bg-primary-700 text-white rounded-lg transition-colors"
                >
                  {t('add')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Duplicate Product Dialog */}
      {showDuplicateDialog && duplicateProduct && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-60">
          <div className="bg-dark-800 rounded-lg shadow-xl w-full max-w-md mx-4">
            <div className="p-6">
              <h3 className="text-lg font-semibold text-white mb-4">
                {t('duplicateProductFound') || 'Duplicate Product Found'}
              </h3>
              <p className="text-dark-200 mb-4">
                {t('duplicateProductMessage') || 'A product with the same name, category, and subcategory already exists. What would you like to do?'}
              </p>
              <div className="bg-dark-700 rounded-lg p-3 mb-4">
                <p className="text-sm text-dark-300">
                  <strong>{t('existingProduct') || 'Existing Product'}:</strong> {duplicateProduct.name}
                </p>
                <p className="text-sm text-dark-300">
                  <strong>{t('category')}:</strong> {duplicateProduct.category} → {duplicateProduct.subcategory}
                </p>
                <p className="text-sm text-dark-300">
                  <strong>{t('price')}:</strong> ${duplicateProduct.price}
                </p>
                <p className="text-sm text-dark-300">
                  <strong>{t('quantity') || 'Quantity'}:</strong> {duplicateProduct.quantity || 1}
                </p>
              </div>
              <div className="flex flex-col space-y-2">
                <button
                  onClick={() => handleDuplicateResolution('replace')}
                  className="w-full px-4 py-2 bg-yellow-600 hover:bg-yellow-700 text-white rounded-lg transition-colors"
                >
                  {t('replaceExisting') || 'Replace Existing Product'}
                </button>
                <button
                  onClick={() => handleDuplicateResolution('modify')}
                  className="w-full px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg transition-colors"
                >
                  {t('modifyAndRetry') || 'Modify Details and Try Again'}
                </button>
                <button
                  onClick={() => handleDuplicateResolution('cancel')}
                  className="w-full px-4 py-2 bg-dark-600 hover:bg-dark-500 text-white rounded-lg transition-colors"
                >
                  {t('cancel')}
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AddProductModal;
