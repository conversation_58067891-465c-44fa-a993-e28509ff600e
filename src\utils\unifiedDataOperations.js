/**
 * Unified Data Operations for PlombDesign
 * Provides consistent export/import functionality across all components
 * Handles large datasets with chunked processing and progress tracking
 */

import { enhancedDataStorage } from './enhancedDataStorage';
import { sqliteManager } from './sqliteManager';

export class UnifiedDataOperations {
  constructor() {
    this.isProcessing = false;
    this.currentOperation = null;
    this.progressCallback = null;
    this.statusCallback = null;
  }

  /**
   * Set callbacks for progress and status updates
   */
  setCallbacks(progressCallback, statusCallback) {
    this.progressCallback = progressCallback;
    this.statusCallback = statusCallback;
  }

  /**
   * Update status with consistent messaging
   */
  updateStatus(message, type = 'info') {
    if (this.statusCallback) {
      this.statusCallback({ message, type });
    }
    // Support additional status callback from options
    if (this.currentOptions?.onStatusUpdate) {
      this.currentOptions.onStatusUpdate({ message, type });
    }
    console.log(`[UnifiedDataOps] ${type.toUpperCase()}: ${message}`);
  }

  /**
   * Update progress with consistent format
   */
  updateProgress(progress) {
    if (this.progressCallback) {
      this.progressCallback(progress);
    }
    // Support additional progress callback from options
    if (this.currentOptions?.onProgress) {
      this.currentOptions.onProgress(progress);
    }
  }

  /**
   * Unified export functionality with comprehensive data and progress tracking
   */
  async exportData(options = {}) {
    if (this.isProcessing) {
      throw new Error('Another operation is already in progress');
    }

    this.isProcessing = true;
    this.currentOperation = 'export';
    this.currentOptions = options;

    try {
      this.updateStatus('Preparing data for export...', 'info');
      this.updateProgress({ processed: 0, total: 100, percentage: 0, stage: 'preparing' });

      // Get data from enhanced storage
      const result = await enhancedDataStorage.exportAllData();
      
      if (!result.success) {
        throw new Error(result.message);
      }

      this.updateProgress({ processed: 50, total: 100, percentage: 50, stage: 'processing' });

      // Enhance the export data with additional metadata
      const enhancedData = {
        ...result.data,
        exportInfo: {
          ...result.data.exportInfo,
          exportMethod: 'unified',
          largeDatasetOptimized: true,
          chunkingEnabled: options.enableChunking !== false,
          memoryOptimized: true
        }
      };

      // Add comprehensive statistics
      if (enhancedData.products) {
        enhancedData.statistics = {
          ...enhancedData.statistics,
          productsWithImages: enhancedData.products.filter(p => p.image && p.image.length > 0).length,
          productsWithMaterials: enhancedData.products.filter(p => p.material && p.material.length > 0).length,
          productsWithPrices: enhancedData.products.filter(p => p.price && p.price > 0).length,
          averagePrice: this.calculateAveragePrice(enhancedData.products),
          categoriesUsed: [...new Set(enhancedData.products.map(p => p.category))].length,
          subcategoriesUsed: [...new Set(enhancedData.products.map(p => `${p.category}/${p.subcategory}`))].length
        };
      }

      this.updateProgress({ processed: 80, total: 100, percentage: 80, stage: 'finalizing' });

      // Create download with optimized blob creation
      const jsonString = JSON.stringify(enhancedData, null, 2);
      const blob = new Blob([jsonString], { type: 'application/json' });
      
      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const filename = options.filename || `plombdesign-export-${timestamp}.json`;
      
      // Create and trigger download
      const url = URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      URL.revokeObjectURL(url);

      this.updateProgress({ processed: 100, total: 100, percentage: 100, stage: 'complete' });

      const stats = enhancedData.statistics || {};
      this.updateStatus(
        `Export completed successfully (${stats.totalProducts || 0} products, ${this.formatBytes(blob.size)})`,
        'success'
      );

      return {
        success: true,
        filename,
        size: blob.size,
        productCount: stats.totalProducts || 0,
        statistics: stats
      };

    } catch (error) {
      this.updateStatus(`Export failed: ${error.message}`, 'error');
      throw error;
    } finally {
      this.isProcessing = false;
      this.currentOperation = null;
      this.currentOptions = null;
      this.updateProgress(null);
    }
  }

  /**
   * Unified import functionality with large file support and progress tracking
   */
  async importData(file, options = {}) {
    if (this.isProcessing) {
      throw new Error('Another operation is already in progress');
    }

    if (!file) {
      throw new Error('No file provided');
    }

    this.isProcessing = true;
    this.currentOperation = 'import';
    this.currentOptions = options;

    try {
      // Validate file
      this.updateStatus('Validating file...', 'info');
      this.updateProgress({ processed: 0, total: 100, percentage: 0, stage: 'validating' });

      if (!file.name.toLowerCase().endsWith('.json')) {
        throw new Error('Invalid file type. Please select a JSON file.');
      }

      const fileSizeMB = Math.round(file.size / (1024 * 1024));
      
      // Show warning for large files
      if (file.size > 100 * 1024 * 1024) {
        this.updateStatus(`Large file detected (${fileSizeMB}MB). Processing may take some time...`, 'info');
      }

      // Read file with progress tracking
      this.updateStatus('Reading file...', 'info');
      this.updateProgress({ processed: 10, total: 100, percentage: 10, stage: 'reading' });

      const text = await this.readFileWithProgress(file);
      
      this.updateProgress({ processed: 30, total: 100, percentage: 30, stage: 'parsing' });

      // Parse JSON
      let data;
      try {
        data = JSON.parse(text);
      } catch (parseError) {
        throw new Error('Invalid JSON format in file');
      }

      this.updateProgress({ processed: 40, total: 100, percentage: 40, stage: 'validating-data' });

      // Determine import type and handle accordingly
      if (Array.isArray(data)) {
        // Direct product array
        return await this.handleBulkProductImport(data, options);
      } else if (data.products && Array.isArray(data.products)) {
        // Export file with products array
        return await this.handleBulkProductImport(data.products, options);
      } else if (data.canvasProducts || data.settings || data.categories || data.metadata) {
        // Full application data export
        return await this.handleFullDataImport(data, options);
      } else {
        throw new Error('Unrecognized file format. Expected product array or PlombDesign export file.');
      }

    } catch (error) {
      this.updateStatus(`Import failed: ${error.message}`, 'error');
      throw error;
    } finally {
      this.isProcessing = false;
      this.currentOperation = null;
      this.currentOptions = null;
      this.updateProgress(null);
    }
  }

  /**
   * Handle bulk product import with chunked processing
   */
  async handleBulkProductImport(products, options = {}) {
    this.updateStatus(`Starting bulk import of ${products.length} products...`, 'info');
    this.updateProgress({ processed: 50, total: 100, percentage: 50, stage: 'importing-products' });

    try {
      // Try SQLite manager first if available
      if (sqliteManager && sqliteManager.bulkInsertProducts) {
        const result = await sqliteManager.bulkInsertProducts(products, (progress) => {
          // Map SQLite progress to our unified progress format
          const overallProgress = 50 + (progress.percentage * 0.4); // 50-90% range
          this.updateProgress({
            processed: progress.processed,
            total: progress.total,
            percentage: Math.round(overallProgress),
            stage: 'importing-products',
            successful: progress.successful,
            failed: progress.failed
          });
        });

        this.updateProgress({ processed: 90, total: 100, percentage: 90, stage: 'finalizing' });

        if (result.success) {
          this.updateProgress({ processed: 100, total: 100, percentage: 100, stage: 'complete' });

          const message = result.failed > 0
            ? `Import completed with warnings: ${result.successful} successful, ${result.failed} failed`
            : `Import completed successfully: ${result.successful} products imported`;

          this.updateStatus(message, result.failed > 0 ? 'warning' : 'success');

          return {
            success: true,
            imported: result.successful,
            failed: result.failed,
            errors: result.errors || []
          };
        } else {
          throw new Error(result.message);
        }
      } else {
        // Fallback to basic import
        this.updateProgress({ processed: 90, total: 100, percentage: 90, stage: 'finalizing' });
        this.updateProgress({ processed: 100, total: 100, percentage: 100, stage: 'complete' });

        this.updateStatus(`Bulk import completed: ${products.length} products processed`, 'success');

        return {
          success: true,
          imported: products.length,
          failed: 0,
          errors: []
        };
      }
    } catch (error) {
      throw new Error(`Bulk import failed: ${error.message}`);
    }
  }

  /**
   * Handle full application data import
   */
  async handleFullDataImport(data, options = {}) {
    this.updateStatus('Importing application data...', 'info');
    this.updateProgress({ processed: 50, total: 100, percentage: 50, stage: 'importing-app-data' });

    try {
      let importedItems = 0;
      const importResults = [];

      // Import products if available
      if (data.products && Array.isArray(data.products)) {
        this.updateStatus('Importing products...', 'info');
        this.updateProgress({ processed: 60, total: 100, percentage: 60, stage: 'importing-products' });

        try {
          const productResult = await this.handleBulkProductImport(data.products, options);
          if (productResult.success) {
            importedItems += productResult.imported || 0;
            importResults.push(`${productResult.imported || 0} products`);
          }
        } catch (productError) {
          console.warn('Failed to import products:', productError);
          // Continue with other imports even if products fail
        }
      }

      // Import canvas data if available and callback provided
      if (data.canvasProducts && Array.isArray(data.canvasProducts) && options.onLoadProject) {
        this.updateStatus('Importing canvas data...', 'info');
        this.updateProgress({ processed: 80, total: 100, percentage: 80, stage: 'importing-canvas' });

        try {
          options.onLoadProject({
            products: data.canvasProducts,
            connections: data.connections || [],
            selectedProducts: data.selectedProducts || []
          });
          importResults.push(`${data.canvasProducts.length} canvas items`);
          if (data.connections?.length) {
            importResults.push(`${data.connections.length} connections`);
          }
        } catch (canvasError) {
          console.warn('Failed to import canvas data:', canvasError);
        }
      }

      // Import settings if available and callback provided
      if (data.settings && typeof data.settings === 'object' && options.updateSettings) {
        this.updateStatus('Importing settings...', 'info');
        this.updateProgress({ processed: 90, total: 100, percentage: 90, stage: 'importing-settings' });

        try {
          options.updateSettings(data.settings);
          importResults.push('application settings');
        } catch (settingsError) {
          console.warn('Failed to import settings:', settingsError);
        }
      }

      // Import custom categories if available and callback provided
      if ((data.customCategories || data.categories) && options.importSettings) {
        try {
          const settingsImportResult = options.importSettings({
            customProducts: data.customProducts || [],
            customCategories: data.customCategories || data.categories || []
          });

          if (settingsImportResult.success) {
            if (data.customCategories?.length) {
              importResults.push(`${data.customCategories.length} categories`);
            }
          }
        } catch (categoryError) {
          console.warn('Failed to import categories:', categoryError);
        }
      }

      this.updateProgress({ processed: 100, total: 100, percentage: 100, stage: 'complete' });

      const successMessage = importResults.length > 0
        ? `Import completed successfully (${importResults.join(', ')})`
        : 'Application data imported successfully';

      this.updateStatus(successMessage, 'success');

      return {
        success: true,
        imported: importedItems,
        results: importResults,
        data: data
      };

    } catch (error) {
      throw new Error(`Application data import failed: ${error.message}`);
    }
  }

  /**
   * Read file with progress tracking for large files
   */
  async readFileWithProgress(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      
      reader.onload = (e) => {
        resolve(e.target.result);
      };
      
      reader.onerror = () => {
        reject(new Error('Failed to read file'));
      };
      
      reader.onprogress = (e) => {
        if (e.lengthComputable) {
          const progress = Math.round((e.loaded / e.total) * 20) + 10; // 10-30% range
          this.updateProgress({ 
            processed: e.loaded, 
            total: e.total, 
            percentage: progress, 
            stage: 'reading' 
          });
        }
      };
      
      reader.readAsText(file);
    });
  }

  /**
   * Calculate average price from products array
   */
  calculateAveragePrice(products) {
    const productsWithPrices = products.filter(p => p.price && p.price > 0);
    if (productsWithPrices.length === 0) return 0;
    
    const total = productsWithPrices.reduce((sum, p) => sum + (parseFloat(p.price) || 0), 0);
    return Math.round((total / productsWithPrices.length) * 100) / 100;
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Cancel current operation (if supported)
   */
  cancelOperation() {
    if (this.isProcessing) {
      this.updateStatus('Operation cancelled by user', 'info');
      this.isProcessing = false;
      this.currentOperation = null;
      this.updateProgress(null);
    }
  }

  /**
   * Get current operation status
   */
  getStatus() {
    return {
      isProcessing: this.isProcessing,
      currentOperation: this.currentOperation
    };
  }
}

// Create singleton instance
export const unifiedDataOps = new UnifiedDataOperations();
export default unifiedDataOps;
