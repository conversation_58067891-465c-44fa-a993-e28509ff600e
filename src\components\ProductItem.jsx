import React, { useState, useEffect, useRef } from 'react';
import ProductHoverPreview from './ProductHoverPreview';
import HighlightedText from './HighlightedText';
import { useDrag } from '../contexts/DragContext';

const ProductItem = ({ product, onDragStart, searchTerm }) => {
  const { startDrag, endDrag, isDraggingFromSidebar } = useDrag();
  const [imageError, setImageError] = useState(false);
  const [showHoverPreview, setShowHoverPreview] = useState(false);
  const [hoverPosition, setHoverPosition] = useState({ x: 0, y: 0 });
  const itemRef = useRef(null);
  const hoverTimeoutRef = useRef(null);

  // Default placeholder image as SVG data URL
  const defaultPlaceholder = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjAiIGhlaWdodD0iNDAiIHZpZXdCb3g9IjAgMCA2MCA0MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjYwIiBoZWlnaHQ9IjQwIiBmaWxsPSIjZjNmNGY2Ii8+CjxwYXRoIGQ9Ik0yNSAyMGwtNS01djEwaDEwdi0xMGwtNSA1eiIgZmlsbD0iIzlmYTZiNyIvPgo8L3N2Zz4=';

  useEffect(() => {
    // Debug log for product images
    console.log(`ProductItem ${product.id} (${product.name}): image - ${product.image ? 'yes' : 'no'}`);

    // Reset error state when product changes
    setImageError(false);
  }, [product]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }
    };
  }, []);

  const handleDragStart = (e) => {
    e.dataTransfer.setData('application/json', JSON.stringify(product));
    e.dataTransfer.effectAllowed = 'copy';

    // Start drag operation from sidebar
    startDrag('sidebar', product);
  };

  const handleMouseDown = (e) => {
    // Only handle drag detection if it's a left mouse button
    if (e.button !== 0) return;

    // Don't interfere with hover if user is just clicking
    const startTime = Date.now();
    let hasMoved = false;

    const handleMouseMove = (moveEvent) => {
      const timeDiff = Date.now() - startTime;
      const distance = Math.sqrt(
        Math.pow(moveEvent.clientX - e.clientX, 2) +
        Math.pow(moveEvent.clientY - e.clientY, 2)
      );

      // Only consider it a drag if moved more than 10px and some time has passed
      if (distance > 10 && timeDiff > 50) {
        hasMoved = true;
        startDrag('sidebar', product);
        // Hide hover preview when dragging starts
        setShowHoverPreview(false);
        cleanup();
      }
    };

    const handleMouseUp = () => {
      cleanup();
    };

    const cleanup = () => {
      document.removeEventListener('mousemove', handleMouseMove);
      document.removeEventListener('mouseup', handleMouseUp);
    };

    document.addEventListener('mousemove', handleMouseMove);
    document.addEventListener('mouseup', handleMouseUp);
  };

  const handleDragEnd = () => {
    endDrag();
  };

  const handleClick = () => {
    // Add to canvas at default position when clicked
    onDragStart(product, { x: 100, y: 100 });
  };

  const handleMouseEnter = (e) => {
    // Don't show hover preview if currently dragging from sidebar
    if (isDraggingFromSidebar()) {
      return;
    }

    // Clear any existing timeout
    if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
    }

    // Immediate hover state
    setShowHoverPreview(true);

    // Calculate fixed horizontal position and mouse-following vertical position
    const calculatePosition = (mouseEvent) => {
      if (!itemRef.current) return;

      const sidebarRect = itemRef.current.closest('.sidebar-container')?.getBoundingClientRect();
      const viewportWidth = window.innerWidth;
      const viewportHeight = window.innerHeight;

      // Fixed horizontal position - always to the right of sidebar with consistent gap
      const gap = 15; // Consistent gap from sidebar
      let x = sidebarRect ? sidebarRect.right + gap : 400; // Fallback if no sidebar found

      // Preview dimensions
      const previewWidth = 450;
      const previewHeight = 400;

      // Adjust horizontal position if preview would go off the right edge
      if (x + previewWidth > viewportWidth) {
        x = sidebarRect ? sidebarRect.left - previewWidth - gap : viewportWidth - previewWidth - gap;
      }

      // Dynamic vertical position - follow mouse cursor
      let y = mouseEvent.clientY;

      // Adjust vertical position to keep preview within viewport bounds
      if (y + previewHeight / 2 > viewportHeight) {
        y = viewportHeight - previewHeight / 2 - 20;
      }

      if (y - previewHeight / 2 < 20) {
        y = previewHeight / 2 + 20;
      }

      setHoverPosition({ x, y });
    };

    // Initial position calculation
    calculatePosition(e);

    // Add mouse move listener to track cursor movement
    const handleMouseMove = (moveEvent) => {
      calculatePosition(moveEvent);
    };

    // Add the mouse move listener to the item
    if (itemRef.current) {
      itemRef.current.addEventListener('mousemove', handleMouseMove);
    }

    // Store the cleanup function
    const cleanup = () => {
      if (itemRef.current) {
        itemRef.current.removeEventListener('mousemove', handleMouseMove);
      }
    };

    // Store cleanup function for later use
    hoverTimeoutRef.current = cleanup;
  };

  const handleMouseLeave = () => {
    // Clean up mouse move listener if it exists
    if (hoverTimeoutRef.current && typeof hoverTimeoutRef.current === 'function') {
      hoverTimeoutRef.current(); // Call cleanup function
      hoverTimeoutRef.current = null;
    } else if (hoverTimeoutRef.current) {
      clearTimeout(hoverTimeoutRef.current);
      hoverTimeoutRef.current = null;
    }
    setShowHoverPreview(false);
  };

  // Determine the image source to use
  const getImageSrc = () => {
    // If there's no image at all, use the placeholder
    if (!product.image) {
      console.log(`Product ${product.id}: No image, using placeholder`);
      return defaultPlaceholder;
    }

    // If the image is a string, use it directly regardless of format
    if (typeof product.image === 'string') {
      // Check for http/https URLs
      if (product.image.startsWith('http')) {
        console.log(`Product ${product.id}: Using remote URL image`);
        return product.image;
      }

      // Check for data URLs (base64)
      if (product.image.startsWith('data:')) {
        console.log(`Product ${product.id}: Using data URL image`);
        return product.image;
      }

      // For any other string format, try to use it but log a warning
      console.log(`Product ${product.id}: Using provided image string (unknown format)`);
      return product.image;
    }

    // Fallback to placeholder for any other case
    console.log(`Product ${product.id}: Invalid image type, using placeholder`);
    return defaultPlaceholder;
  };

  return (
    <>
      <div
        ref={itemRef}
        draggable
        onDragStart={handleDragStart}
        onDragEnd={handleDragEnd}
        onMouseDown={handleMouseDown}
        onClick={handleClick}
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        className={`product-grid-item group cursor-pointer flex flex-col items-center hover:opacity-75 transition-all duration-200 hover:scale-105 ${showHoverPreview ? 'ring-2 ring-primary-500' : ''}`}
        style={{ pointerEvents: 'auto' }}
        title={`${product.name} - ${product.price}`}
      >
        {/* Product Image - Larger size with proper aspect ratio */}
        <div className="w-20 h-16 bg-white rounded-lg flex items-center justify-center overflow-hidden mb-2 shadow-sm border border-gray-200">
          {!imageError ? (
            <img
              src={getImageSrc()}
              alt={product.name}
              className="product-image"
              style={{
                width: '100%',
                height: '100%',
                objectFit: 'contain', // Maintain aspect ratio without stretching
                objectPosition: 'center', // Center the image within container
                display: 'block' // Prevent inline spacing issues
              }}
              onError={(e) => {
                console.log(`Image error for ${product.name}: loading failed, URL: ${e.target.src.substring(0, 30)}...`);
                // If the image fails to load and isn't already the default, try the default
                if (e.target.src !== defaultPlaceholder) {
                  console.log(`Falling back to default placeholder for ${product.name}`);
                  e.target.src = defaultPlaceholder;
                  // Only set error state if the default also fails
                  e.target.onerror = () => {
                    console.log(`Default placeholder also failed for ${product.name}`);
                    setImageError(true);
                  };
                } else {
                  setImageError(true);
                }
              }}
            />
          ) : (
            <div className="w-full h-full bg-gray-100 rounded flex items-center justify-center text-xs text-gray-500">
              IMG
            </div>
          )}
        </div>

        {/* Product Name */}
        <div className="text-xs font-medium text-white text-center line-clamp-2 overflow-hidden break-words hyphens-auto max-w-full px-1" style={{ lineHeight: '1.3', wordWrap: 'break-word', overflowWrap: 'break-word', hyphens: 'auto' }}>
          <HighlightedText
            text={product.name}
            searchTerm={searchTerm}
            highlightClassName="bg-yellow-400 text-yellow-900 px-1 rounded"
          />
        </div>

        {/* Price and Quantity */}
        <div className="text-xs font-semibold text-primary-400 mt-1">
          {product.price}
        </div>
        {product.quantity && product.quantity > 1 && (
          <div className="text-xs text-orange-400 font-medium">
            Qty: {product.quantity}
          </div>
        )}
      </div>

      {/* Hover Preview */}
      <ProductHoverPreview
        product={product}
        position={hoverPosition}
        isVisible={showHoverPreview}
        onImageError={() => setImageError(true)}
      />
    </>
  );
};

export default ProductItem;
