import React, { useState, useEffect } from 'react';
import { Database, Plus, Download, Upload, Trash2, RefreshCw, CheckCircle, XCircle } from 'lucide-react';
import AddProductModal from './AddProductModal';
import { useData } from '../contexts/DataContext';
import { useSettings } from '../contexts/SettingsContext';

const DatabaseIntegrationTest = () => {
  const [showAddModal, setShowAddModal] = useState(false);
  const [testResults, setTestResults] = useState([]);
  const [isRunningTests, setIsRunningTests] = useState(false);
  const [testProducts, setTestProducts] = useState([]);

  const {
    saveProductToDatabase,
    getProductsFromDatabase,
    searchProductsInDatabase,
    refreshDatabaseStats,
    databaseStats,
    sqliteEnabled,
    exportData,
    importData,
    clearAllData
  } = useData();

  const { customProducts } = useSettings();

  // Test product data
  const sampleProducts = [
    {
      name: 'Test Product 1',
      category: 'Test Category',
      subcategory: 'Test Subcategory',
      price: 10.99,
      quantity: 5,
      diameter: '10mm',
      material: 'Steel',
      image: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=='
    },
    {
      name: 'Test Product 2',
      category: 'Test Category',
      subcategory: 'Another Subcategory',
      price: 25.50,
      quantity: 3,
      diameter: '15mm',
      material: 'Aluminum',
      image: null
    }
  ];

  const addTestResult = (test, success, message) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      message,
      timestamp: new Date().toISOString()
    }]);
  };

  const runIntegrationTests = async () => {
    setIsRunningTests(true);
    setTestResults([]);

    try {
      // Test 1: Database Connection
      addTestResult('Database Connection', sqliteEnabled, 
        sqliteEnabled ? 'SQLite database is enabled' : 'Using localStorage fallback');

      // Test 2: Save Products via AddProductModal simulation
      addTestResult('Product Save Test', true, 'Starting product save tests...');
      
      for (let i = 0; i < sampleProducts.length; i++) {
        const product = sampleProducts[i];
        try {
          const result = await saveProductToDatabase(product);
          addTestResult(`Save Product ${i + 1}`, result.success, 
            result.success ? `Product "${product.name}" saved successfully` : result.message);
          
          if (result.success) {
            setTestProducts(prev => [...prev, { ...product, id: result.productId }]);
          }
        } catch (error) {
          addTestResult(`Save Product ${i + 1}`, false, error.message);
        }
      }

      // Test 3: Retrieve Products
      try {
        const retrieveResult = await getProductsFromDatabase();
        addTestResult('Retrieve Products', retrieveResult.success,
          retrieveResult.success ? 
            `Retrieved ${retrieveResult.products.length} products from database` : 
            retrieveResult.message);
      } catch (error) {
        addTestResult('Retrieve Products', false, error.message);
      }

      // Test 4: Search Products
      try {
        const searchResult = await searchProductsInDatabase('Test');
        addTestResult('Search Products', searchResult.success,
          searchResult.success ? 
            `Found ${searchResult.products.length} products matching "Test"` : 
            searchResult.message);
      } catch (error) {
        addTestResult('Search Products', false, error.message);
      }

      // Test 5: Database Statistics
      try {
        const statsResult = await refreshDatabaseStats();
        addTestResult('Database Statistics', statsResult.success,
          statsResult.success ? 
            'Database statistics refreshed successfully' : 
            statsResult.message);
      } catch (error) {
        addTestResult('Database Statistics', false, error.message);
      }

      // Test 6: Export Data
      try {
        const exportResult = await exportData();
        addTestResult('Export Data', exportResult.success,
          exportResult.success ? 
            `Data exported successfully (${exportResult.productCount} products)` : 
            'Export failed');
      } catch (error) {
        addTestResult('Export Data', false, error.message);
      }

      addTestResult('Integration Tests', true, 'All integration tests completed');

    } catch (error) {
      addTestResult('Integration Tests', false, `Test suite failed: ${error.message}`);
    } finally {
      setIsRunningTests(false);
    }
  };

  const clearTestData = async () => {
    try {
      const result = await clearAllData();
      addTestResult('Clear Test Data', result.success, 
        result.success ? 'Test data cleared successfully' : result.message);
      setTestProducts([]);
    } catch (error) {
      addTestResult('Clear Test Data', false, error.message);
    }
  };

  const handleProductSubmit = async (productData) => {
    try {
      const result = await saveProductToDatabase(productData);
      if (result.success) {
        addTestResult('AddProductModal Integration', true, 
          `Product "${productData.name}" created via AddProductModal and saved to database`);
        setTestProducts(prev => [...prev, { ...productData, id: result.productId }]);
      } else {
        addTestResult('AddProductModal Integration', false, result.message);
      }
    } catch (error) {
      addTestResult('AddProductModal Integration', false, error.message);
    }
    setShowAddModal(false);
  };

  return (
    <div className="min-h-screen bg-dark-900 p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-white mb-8 flex items-center">
          <Database className="w-8 h-8 mr-3" />
          Database Integration Test Suite
        </h1>

        {/* Status Cards */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          <div className="bg-dark-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Database Status</h3>
            <div className="flex items-center">
              {sqliteEnabled ? (
                <CheckCircle className="w-5 h-5 text-green-500 mr-2" />
              ) : (
                <XCircle className="w-5 h-5 text-yellow-500 mr-2" />
              )}
              <span className="text-dark-200">
                {sqliteEnabled ? 'SQLite Enabled' : 'localStorage Fallback'}
              </span>
            </div>
          </div>

          <div className="bg-dark-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Products in Database</h3>
            <div className="text-2xl font-bold text-primary-400">
              {databaseStats?.sqlite?.totalProducts || customProducts.length || 0}
            </div>
          </div>

          <div className="bg-dark-800 rounded-lg p-6">
            <h3 className="text-lg font-semibold text-white mb-2">Test Products Created</h3>
            <div className="text-2xl font-bold text-green-400">
              {testProducts.length}
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-4 mb-8">
          <button
            onClick={() => setShowAddModal(true)}
            className="flex items-center gap-2 px-6 py-3 bg-primary-600 hover:bg-primary-700 text-white rounded-lg font-medium transition-colors"
          >
            <Plus className="w-5 h-5" />
            Test AddProductModal
          </button>

          <button
            onClick={runIntegrationTests}
            disabled={isRunningTests}
            className="flex items-center gap-2 px-6 py-3 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg font-medium transition-colors"
          >
            <RefreshCw className={`w-5 h-5 ${isRunningTests ? 'animate-spin' : ''}`} />
            Run Integration Tests
          </button>

          <button
            onClick={clearTestData}
            className="flex items-center gap-2 px-6 py-3 bg-red-600 hover:bg-red-700 text-white rounded-lg font-medium transition-colors"
          >
            <Trash2 className="w-5 h-5" />
            Clear Test Data
          </button>
        </div>

        {/* Test Results */}
        <div className="bg-dark-800 rounded-lg p-6">
          <h2 className="text-xl font-semibold text-white mb-4">Test Results</h2>
          
          {testResults.length === 0 ? (
            <p className="text-dark-400">No tests run yet. Click "Run Integration Tests" to start.</p>
          ) : (
            <div className="space-y-2 max-h-96 overflow-y-auto">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`flex items-start gap-3 p-3 rounded-lg ${
                    result.success ? 'bg-green-900/20 border border-green-500/30' : 'bg-red-900/20 border border-red-500/30'
                  }`}
                >
                  {result.success ? (
                    <CheckCircle className="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
                  ) : (
                    <XCircle className="w-5 h-5 text-red-500 mt-0.5 flex-shrink-0" />
                  )}
                  <div className="flex-1">
                    <div className="font-medium text-white">{result.test}</div>
                    <div className={`text-sm ${result.success ? 'text-green-300' : 'text-red-300'}`}>
                      {result.message}
                    </div>
                    <div className="text-xs text-dark-400 mt-1">
                      {new Date(result.timestamp).toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>

        {/* Test Products List */}
        {testProducts.length > 0 && (
          <div className="bg-dark-800 rounded-lg p-6 mt-6">
            <h2 className="text-xl font-semibold text-white mb-4">Created Test Products</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {testProducts.map((product, index) => (
                <div key={index} className="bg-dark-700 rounded-lg p-4">
                  <h3 className="font-medium text-white">{product.name}</h3>
                  <p className="text-dark-300 text-sm">{product.category} → {product.subcategory}</p>
                  <p className="text-dark-300 text-sm">Price: ${product.price}</p>
                  <p className="text-dark-300 text-sm">Quantity: {product.quantity}</p>
                  {product.material && (
                    <p className="text-dark-300 text-sm">Material: {product.material}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}
      </div>

      {/* AddProductModal */}
      {showAddModal && (
        <AddProductModal
          onClose={() => setShowAddModal(false)}
          onSubmit={handleProductSubmit}
        />
      )}
    </div>
  );
};

export default DatabaseIntegrationTest;
