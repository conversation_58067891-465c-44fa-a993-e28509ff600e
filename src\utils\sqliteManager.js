/**
 * SQLite Database Manager for PlombDesign
 * Handles database initialization, product storage, and image management
 */

import initSqlJs from 'sql.js';

class SQLiteManager {
  constructor() {
    this.db = null;
    this.SQL = null;
    this.isInitialized = false;
    this.saveDataPath = 'PlombDesign/Save Data';
    this.imagesPath = `${this.saveDataPath}/Images`;
    this.dbPath = `${this.saveDataPath}/plombdesign.db`;
  }

  /**
   * Initialize SQLite and create database
   */
  async initialize() {
    try {
      console.log('Initializing SQLite database...');
      
      // Initialize SQL.js
      this.SQL = await initSqlJs({
        locateFile: file => `https://sql.js.org/dist/${file}`
      });

      // Create or load database
      await this.createDatabase();
      await this.createTables();
      
      this.isInitialized = true;
      console.log('SQLite database initialized successfully');
      
      return { success: true, message: 'Database initialized successfully' };
    } catch (error) {
      console.error('Error initializing SQLite:', error);
      return { success: false, message: `Database initialization failed: ${error.message}` };
    }
  }

  /**
   * Create database file or load existing one
   */
  async createDatabase() {
    try {
      // Check if database file exists
      const dbExists = await this.checkFileExists(this.dbPath);
      
      if (dbExists) {
        // Load existing database
        const dbData = await this.loadDatabaseFile();
        this.db = new this.SQL.Database(dbData);
        console.log('Loaded existing database');
      } else {
        // Create new database
        this.db = new this.SQL.Database();
        console.log('Created new database');
      }
    } catch (error) {
      console.error('Error creating/loading database:', error);
      throw error;
    }
  }

  /**
   * Create database tables with proper schema
   */
  async createTables() {
    try {
      // Products table with UNIQUE constraint for duplicate detection
      const createProductsTable = `
        CREATE TABLE IF NOT EXISTS products (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category TEXT NOT NULL,
          subcategory TEXT NOT NULL,
          product_name TEXT NOT NULL,
          image_path TEXT,
          material TEXT,
          diameter TEXT,
          size TEXT,
          price REAL DEFAULT 0,
          quantity INTEGER DEFAULT 0,
          last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          metadata TEXT,
          UNIQUE(category, subcategory, product_name)
        );
      `;

      // Categories table for organization
      const createCategoriesTable = `
        CREATE TABLE IF NOT EXISTS categories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT UNIQUE NOT NULL,
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `;

      // Subcategories table
      const createSubcategoriesTable = `
        CREATE TABLE IF NOT EXISTS subcategories (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          category_id INTEGER NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          FOREIGN KEY (category_id) REFERENCES categories (id),
          UNIQUE(category_id, name)
        );
      `;

      // Canvas state table for saving workspace layouts
      const createCanvasStateTable = `
        CREATE TABLE IF NOT EXISTS canvas_state (
          id INTEGER PRIMARY KEY AUTOINCREMENT,
          name TEXT NOT NULL,
          canvas_data TEXT NOT NULL,
          connections_data TEXT,
          metadata TEXT,
          created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
          last_modified DATETIME DEFAULT CURRENT_TIMESTAMP
        );
      `;

      // Execute table creation
      this.db.exec(createProductsTable);
      this.db.exec(createCategoriesTable);
      this.db.exec(createSubcategoriesTable);
      this.db.exec(createCanvasStateTable);

      // Create indexes for performance
      this.createIndexes();

      console.log('Database tables created successfully');
    } catch (error) {
      console.error('Error creating tables:', error);
      throw error;
    }
  }

  /**
   * Create indexes for better performance
   */
  createIndexes() {
    try {
      const indexes = [
        'CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);',
        'CREATE INDEX IF NOT EXISTS idx_products_subcategory ON products(subcategory);',
        'CREATE INDEX IF NOT EXISTS idx_products_name ON products(product_name);',
        'CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);',
        'CREATE INDEX IF NOT EXISTS idx_products_last_updated ON products(last_updated);',
        'CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);',
        'CREATE INDEX IF NOT EXISTS idx_subcategories_category ON subcategories(category_id);'
      ];

      indexes.forEach(indexSQL => {
        this.db.exec(indexSQL);
      });

      console.log('Database indexes created successfully');
    } catch (error) {
      console.error('Error creating indexes:', error);
    }
  }

  /**
   * Insert or replace product (handles duplicates)
   */
  async insertOrReplaceProduct(productData) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const {
        category,
        subcategory,
        product_name,
        image_data,
        material = '',
        diameter = '',
        size = '',
        price = 0,
        quantity = 0,
        metadata = {}
      } = productData;

      // Save image if provided
      let imagePath = null;
      if (image_data) {
        imagePath = await this.saveImage(image_data, product_name);
      }

      // Insert or replace product
      const stmt = this.db.prepare(`
        INSERT OR REPLACE INTO products 
        (category, subcategory, product_name, image_path, material, diameter, size, price, quantity, metadata, last_updated)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
      `);

      const result = stmt.run([
        category,
        subcategory,
        product_name,
        imagePath,
        material,
        diameter,
        size,
        price,
        quantity,
        JSON.stringify(metadata)
      ]);

      stmt.free();

      // Save database to file
      await this.saveDatabaseFile();

      console.log(`Product ${product_name} saved successfully`);
      return { 
        success: true, 
        productId: result.lastInsertRowid,
        message: 'Product saved successfully',
        imagePath
      };

    } catch (error) {
      console.error('Error inserting/replacing product:', error);
      return { 
        success: false, 
        message: `Failed to save product: ${error.message}` 
      };
    }
  }

  /**
   * Get all products with optional filtering
   */
  async getProducts(filters = {}) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      let query = 'SELECT * FROM products';
      const conditions = [];
      const params = [];

      // Add filters
      if (filters.category) {
        conditions.push('category = ?');
        params.push(filters.category);
      }
      if (filters.subcategory) {
        conditions.push('subcategory = ?');
        params.push(filters.subcategory);
      }
      if (filters.search) {
        conditions.push('(product_name LIKE ? OR material LIKE ?)');
        params.push(`%${filters.search}%`, `%${filters.search}%`);
      }

      if (conditions.length > 0) {
        query += ' WHERE ' + conditions.join(' AND ');
      }

      query += ' ORDER BY category, subcategory, product_name';

      const stmt = this.db.prepare(query);
      const result = stmt.all(params);
      stmt.free();

      // Parse metadata and load images
      const products = await Promise.all(result.map(async (row) => {
        const product = {
          ...row,
          metadata: row.metadata ? JSON.parse(row.metadata) : {}
        };

        // Load image if path exists
        if (row.image_path) {
          product.image = await this.loadImage(row.image_path);
        }

        return product;
      }));

      return { success: true, products };

    } catch (error) {
      console.error('Error getting products:', error);
      return { success: false, message: error.message, products: [] };
    }
  }

  /**
   * Save image to file system (simulated with localStorage for web)
   */
  async saveImage(imageData, productName) {
    try {
      // Create unique filename
      const timestamp = Date.now();
      const sanitizedName = productName.replace(/[^a-zA-Z0-9]/g, '_');
      const filename = `${sanitizedName}_${timestamp}.png`;
      const imagePath = `${this.imagesPath}/${filename}`;

      // For web environment, we'll use localStorage to simulate file storage
      // In a real desktop app, this would write to actual files
      const imageKey = `plomb_image_${filename}`;
      localStorage.setItem(imageKey, imageData);

      console.log(`Image saved: ${imagePath}`);
      return imagePath;

    } catch (error) {
      console.error('Error saving image:', error);
      throw error;
    }
  }

  /**
   * Load image from file system (simulated with localStorage for web)
   */
  async loadImage(imagePath) {
    try {
      const filename = imagePath.split('/').pop();
      const imageKey = `plomb_image_${filename}`;
      const imageData = localStorage.getItem(imageKey);
      
      return imageData || null;

    } catch (error) {
      console.error('Error loading image:', error);
      return null;
    }
  }

  /**
   * Check if file exists (simulated for web)
   */
  async checkFileExists(filePath) {
    try {
      // In web environment, check localStorage for database
      if (filePath.includes('.db')) {
        return localStorage.getItem('plomb_database') !== null;
      }
      return false;
    } catch (error) {
      return false;
    }
  }

  /**
   * Load database file (simulated with localStorage for web)
   */
  async loadDatabaseFile() {
    try {
      const dbData = localStorage.getItem('plomb_database');
      if (dbData) {
        // Convert base64 back to Uint8Array
        const binaryString = atob(dbData);
        const bytes = new Uint8Array(binaryString.length);
        for (let i = 0; i < binaryString.length; i++) {
          bytes[i] = binaryString.charCodeAt(i);
        }
        return bytes;
      }
      return null;
    } catch (error) {
      console.error('Error loading database file:', error);
      return null;
    }
  }

  /**
   * Save database file (simulated with localStorage for web)
   */
  async saveDatabaseFile() {
    try {
      if (this.db) {
        const data = this.db.export();
        // Convert Uint8Array to base64 for localStorage
        const binaryString = String.fromCharCode.apply(null, data);
        const base64Data = btoa(binaryString);
        localStorage.setItem('plomb_database', base64Data);
        console.log('Database saved to localStorage');
      }
    } catch (error) {
      console.error('Error saving database file:', error);
    }
  }

  /**
   * Get database statistics
   */
  async getDatabaseStats() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const stats = {};

      // Product count by category
      const categoryStats = this.db.exec(`
        SELECT category, COUNT(*) as count
        FROM products
        GROUP BY category
        ORDER BY count DESC
      `);

      stats.productsByCategory = categoryStats[0] ?
        categoryStats[0].values.map(row => ({ category: row[0], count: row[1] })) : [];

      // Total products
      const totalProducts = this.db.exec('SELECT COUNT(*) as total FROM products');
      stats.totalProducts = totalProducts[0] ? totalProducts[0].values[0][0] : 0;

      // Database size (approximate)
      const dbData = this.db.export();
      stats.databaseSize = dbData.length;
      stats.formattedSize = this.formatBytes(dbData.length);

      // Recent products
      const recentProducts = this.db.exec(`
        SELECT product_name, category, last_updated
        FROM products
        ORDER BY last_updated DESC
        LIMIT 10
      `);

      stats.recentProducts = recentProducts[0] ?
        recentProducts[0].values.map(row => ({
          name: row[0],
          category: row[1],
          lastUpdated: row[2]
        })) : [];

      return { success: true, stats };

    } catch (error) {
      console.error('Error getting database stats:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Search products with advanced filtering
   */
  async searchProducts(searchTerm, options = {}) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const {
        categories = [],
        subcategories = [],
        priceRange = null,
        sortBy = 'product_name',
        sortOrder = 'ASC',
        limit = 100
      } = options;

      let query = `
        SELECT * FROM products
        WHERE (product_name LIKE ? OR material LIKE ? OR diameter LIKE ?)
      `;
      const params = [`%${searchTerm}%`, `%${searchTerm}%`, `%${searchTerm}%`];

      // Add category filter
      if (categories.length > 0) {
        const placeholders = categories.map(() => '?').join(',');
        query += ` AND category IN (${placeholders})`;
        params.push(...categories);
      }

      // Add subcategory filter
      if (subcategories.length > 0) {
        const placeholders = subcategories.map(() => '?').join(',');
        query += ` AND subcategory IN (${placeholders})`;
        params.push(...subcategories);
      }

      // Add price range filter
      if (priceRange) {
        if (priceRange.min !== undefined) {
          query += ' AND price >= ?';
          params.push(priceRange.min);
        }
        if (priceRange.max !== undefined) {
          query += ' AND price <= ?';
          params.push(priceRange.max);
        }
      }

      // Add sorting
      query += ` ORDER BY ${sortBy} ${sortOrder}`;

      // Add limit
      query += ` LIMIT ?`;
      params.push(limit);

      const stmt = this.db.prepare(query);
      const result = stmt.all(params);
      stmt.free();

      // Process results
      const products = await Promise.all(result.map(async (row) => {
        const product = {
          ...row,
          metadata: row.metadata ? JSON.parse(row.metadata) : {}
        };

        if (row.image_path) {
          product.image = await this.loadImage(row.image_path);
        }

        return product;
      }));

      return { success: true, products, count: products.length };

    } catch (error) {
      console.error('Error searching products:', error);
      return { success: false, message: error.message, products: [] };
    }
  }

  /**
   * Bulk insert products with progress tracking
   */
  async bulkInsertProducts(products, onProgress = null) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const total = products.length;
      let processed = 0;
      let successful = 0;
      let failed = 0;
      const errors = [];

      console.log(`Starting bulk insert of ${total} products...`);

      // Process in batches to avoid memory issues
      const batchSize = 100;
      for (let i = 0; i < products.length; i += batchSize) {
        const batch = products.slice(i, i + batchSize);

        for (const product of batch) {
          try {
            const result = await this.insertOrReplaceProduct(product);
            if (result.success) {
              successful++;
            } else {
              failed++;
              errors.push({ product: product.product_name, error: result.message });
            }
          } catch (error) {
            failed++;
            errors.push({ product: product.product_name, error: error.message });
          }

          processed++;

          // Report progress
          if (onProgress) {
            onProgress({
              processed,
              total,
              successful,
              failed,
              percentage: Math.round((processed / total) * 100)
            });
          }
        }

        // Small delay between batches to prevent blocking
        await new Promise(resolve => setTimeout(resolve, 10));
      }

      console.log(`Bulk insert completed: ${successful} successful, ${failed} failed`);

      return {
        success: true,
        processed,
        successful,
        failed,
        errors
      };

    } catch (error) {
      console.error('Error in bulk insert:', error);
      return {
        success: false,
        message: error.message,
        processed: 0,
        successful: 0,
        failed: 0,
        errors: []
      };
    }
  }

  /**
   * Delete product by ID
   */
  async deleteProduct(productId) {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Get product info first to delete image
      const stmt = this.db.prepare('SELECT image_path FROM products WHERE id = ?');
      const result = stmt.get([productId]);
      stmt.free();

      if (result && result.image_path) {
        // Delete image file
        const filename = result.image_path.split('/').pop();
        const imageKey = `plomb_image_${filename}`;
        localStorage.removeItem(imageKey);
      }

      // Delete product
      const deleteStmt = this.db.prepare('DELETE FROM products WHERE id = ?');
      const deleteResult = deleteStmt.run([productId]);
      deleteStmt.free();

      // Save database
      await this.saveDatabaseFile();

      return {
        success: true,
        message: 'Product deleted successfully',
        rowsAffected: deleteResult.changes
      };

    } catch (error) {
      console.error('Error deleting product:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Format bytes to human readable format
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Export database to JSON
   */
  async exportToJSON() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      const products = await this.getProducts();
      const stats = await this.getDatabaseStats();

      const exportData = {
        exportInfo: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          application: 'PlombDesign SQLite',
          totalProducts: stats.stats?.totalProducts || 0
        },
        products: products.products || [],
        statistics: stats.stats || {}
      };

      return { success: true, data: exportData };

    } catch (error) {
      console.error('Error exporting to JSON:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Clear all data
   */
  async clearAllData() {
    try {
      if (!this.isInitialized) {
        await this.initialize();
      }

      // Clear all tables
      this.db.exec('DELETE FROM products');
      this.db.exec('DELETE FROM categories');
      this.db.exec('DELETE FROM subcategories');
      this.db.exec('DELETE FROM canvas_state');

      // Clear images from localStorage
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith('plomb_image_')) {
          localStorage.removeItem(key);
        }
      });

      // Save database
      await this.saveDatabaseFile();

      console.log('All data cleared successfully');
      return { success: true, message: 'All data cleared successfully' };

    } catch (error) {
      console.error('Error clearing data:', error);
      return { success: false, message: error.message };
    }
  }
}

// Create singleton instance
export const sqliteManager = new SQLiteManager();
export default sqliteManager;
