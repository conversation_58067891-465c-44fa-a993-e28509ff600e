import { useState, useEffect, useRef } from 'react';
import {
  Plus,
  X,
  Upload,
  Edit,
  Trash2,
  Package,
  FileSpreadsheet,
  AlertCircle,
  CheckCircle,
  Info,
  Merge,
  Copy,
  RefreshCw,
  Camera,
  Link,
  Image as ImageIcon,
  Save
} from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';

const ProductTemplateBuilder = ({ isOpen, onClose }) => {
  const { t } = useLanguage();
  const {
    getAllCategories,
    addCustomProduct,
    customProducts,
    removeCustomProduct,
    updateCustomProduct,
    addCustomCategory,
    addCustomSubcategory,
    replaceProductCatalogWithTemplate
  } = useSettings();

  // Template state
  const [templateProducts, setTemplateProducts] = useState([]);
  const [editingProductIndex, setEditingProductIndex] = useState(null);

  // Form state
  const [productForm, setProductForm] = useState({
    name: '',
    category: '',
    subcategory: '',
    price: '',
    quantity: '1',
    size: '',
    material: '',
    image: ''
  });

  // UI state
  const [imagePreview, setImagePreview] = useState('');
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [formErrors, setFormErrors] = useState({});
  const [statusMessage, setStatusMessage] = useState(null);

  // Enhanced image handling state
  const [imageInputMethod, setImageInputMethod] = useState('upload'); // 'upload', 'camera', 'url'
  const [imageUrl, setImageUrl] = useState('');
  const [imageUploadError, setImageUploadError] = useState('');
  const [showCameraStream, setShowCameraStream] = useState(false);
  const [isCameraReady, setIsCameraReady] = useState(false);
  const [showCameraReadyIndicator, setShowCameraReadyIndicator] = useState(false);

  // Refs for camera functionality
  const videoRef = useRef(null);
  const canvasRef = useRef(null);
  const streamRef = useRef(null);

  // Duplicate detection modal state
  const [showDuplicateModal, setShowDuplicateModal] = useState(false);
  const [duplicateConflict, setDuplicateConflict] = useState(null);
  const [pendingProductData, setPendingProductData] = useState(null);

  // Category/Subcategory creation state
  const [isAddingNewCategory, setIsAddingNewCategory] = useState(false);
  const [isAddingNewSubcategory, setIsAddingNewSubcategory] = useState(false);
  const [newCategoryName, setNewCategoryName] = useState('');
  const [newSubcategoryName, setNewSubcategoryName] = useState('');
  const [isCreatingCategory, setIsCreatingCategory] = useState(false);
  const [isCreatingSubcategory, setIsCreatingSubcategory] = useState(false);
  const [categoryCreationError, setCategoryCreationError] = useState('');
  const [subcategoryCreationError, setSubcategoryCreationError] = useState('');

  // Auto-save template to localStorage
  useEffect(() => {
    if (templateProducts.length > 0) {
      localStorage.setItem('plomdesign-template-builder', JSON.stringify(templateProducts));
    }
  }, [templateProducts]);

  // Load saved template on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem('plomdesign-template-builder');
      if (saved) {
        setTemplateProducts(JSON.parse(saved));
      }
    } catch (error) {
      console.warn('Failed to load saved template:', error);
    }
  }, []);

  // Cleanup camera stream on unmount
  useEffect(() => {
    return () => {
      stopCameraStream();
    };
  }, []);

  // Reset form
  const resetProductForm = () => {
    setProductForm({
      name: '',
      category: '',
      subcategory: '',
      price: '',
      quantity: '1',
      size: '',
      material: '',
      image: ''
    });
    setImagePreview('');
    setFormErrors({});
    setEditingProductIndex(null);

    // Reset enhanced image state
    setImageInputMethod('upload');
    setImageUrl('');
    setImageUploadError('');
    setShowCameraStream(false);
    stopCameraStream();

    // Reset category/subcategory creation states
    setIsAddingNewCategory(false);
    setIsAddingNewSubcategory(false);
    setNewCategoryName('');
    setNewSubcategoryName('');
    setCategoryCreationError('');
    setSubcategoryCreationError('');
    setIsCreatingCategory(false);
    setIsCreatingSubcategory(false);
  };

  // Validate form
  const validateProductForm = () => {
    const errors = {};

    if (!productForm.name.trim()) {
      errors.name = t('requiredField') || 'Required field';
    } else if (templateProducts.some((p, index) =>
      p.name.toLowerCase() === productForm.name.toLowerCase() && index !== editingProductIndex
    )) {
      errors.name = t('duplicateProduct') || 'Product with this name already exists';
    }

    if (!productForm.category) {
      errors.category = t('requiredField') || 'Required field';
    }

    if (!productForm.subcategory) {
      errors.subcategory = t('requiredField') || 'Required field';
    }

    // Validate quantity
    const quantity = parseInt(productForm.quantity);
    if (!productForm.quantity || isNaN(quantity) || quantity < 1) {
      errors.quantity = t('validQuantityRequired') || 'Valid quantity (minimum 1) is required';
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  // Show status message
  const showStatus = (type, message, duration = 3000) => {
    setStatusMessage({ type, message });
    setTimeout(() => setStatusMessage(null), duration);
  };

  // Handle image upload
  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file size (2MB limit)
    if (file.size > 2 * 1024 * 1024) {
      showStatus('error', t('imageTooLarge') || 'Image file is too large (max 2MB)');
      return;
    }

    // Validate file type
    const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/svg+xml'];
    if (!validTypes.includes(file.type)) {
      showStatus('error', t('unsupportedImageFormat') || 'Unsupported image format');
      return;
    }

    setIsProcessingImage(true);
    showStatus('info', t('processingImage') || 'Processing image...');

    const reader = new FileReader();
    reader.onload = (e) => {
      const base64Data = e.target.result;
      setImagePreview(base64Data);
      setProductForm(prev => ({ ...prev, image: base64Data }));
      setIsProcessingImage(false);
      showStatus('success', t('imageProcessed') || 'Image processed successfully', 2000);
    };

    reader.onerror = () => {
      setIsProcessingImage(false);
      showStatus('error', t('unsupportedImageFormat') || 'Error processing image');
    };

    reader.readAsDataURL(file);
    event.target.value = '';
  };

  // Remove image
  const removeImage = () => {
    setImagePreview('');
    setProductForm(prev => ({ ...prev, image: '' }));
    setImageUrl('');
    setImageUploadError('');
    stopCameraStream();
  };

  // Enhanced image handling functions
  const validateImageFile = (file) => {
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp', 'image/svg+xml'];

    if (!allowedTypes.includes(file.type)) {
      return t('Unsupported image format. Please use PNG, JPG, JPEG, GIF, WebP, or SVG.') || 'Unsupported image format. Please use PNG, JPG, JPEG, GIF, WebP, or SVG.';
    }

    if (file.size > maxSize) {
      return t('Image size must be less than 2MB') || 'Image size must be less than 2MB';
    }

    return null;
  };

  const processImageFile = (file) => {
    const validationError = validateImageFile(file);
    if (validationError) {
      setImageUploadError(validationError);
      showStatus('error', validationError);
      return;
    }

    setImageUploadError('');
    setIsProcessingImage(true);
    showStatus('info', t('processingImage') || 'Processing image...');

    const reader = new FileReader();
    reader.onload = (e) => {
      const base64Data = e.target.result;
      setImagePreview(base64Data);
      setProductForm(prev => ({ ...prev, image: base64Data }));
      setIsProcessingImage(false);
      showStatus('success', t('imageProcessed') || 'Image processed successfully', 2000);
    };

    reader.onerror = () => {
      setIsProcessingImage(false);
      setImageUploadError(t('Error processing image') || 'Error processing image');
      showStatus('error', t('Error processing image') || 'Error processing image');
    };

    reader.readAsDataURL(file);
  };

  const handleImageUrlInput = () => {
    const url = imageUrl.trim();

    if (!url) {
      setImageUploadError(t('Please enter a valid image URL') || 'Please enter a valid image URL');
      return;
    }

    // Basic URL validation
    const urlPattern = /^(https?:\/\/)?([\da-z\.-]+)\.([a-z\.]{2,6})([\/\w \.-]*)*\/?$/;
    const isDataUrl = url.startsWith('data:image/');

    if (!urlPattern.test(url) && !isDataUrl) {
      setImageUploadError(t('Please enter a valid URL (e.g., https://example.com/image.jpg)') || 'Please enter a valid URL (e.g., https://example.com/image.jpg)');
      return;
    }

    // Ensure URL has protocol for external URLs
    let finalUrl = url;
    if (!isDataUrl && !url.startsWith('http://') && !url.startsWith('https://')) {
      finalUrl = 'https://' + url;
    }

    setImageUploadError('');
    setIsProcessingImage(true);
    showStatus('info', t('Loading image from URL...') || 'Loading image from URL...');

    // Test if the URL is valid and the image loads
    const testImage = new Image();

    // Set a timeout for the image loading
    const timeout = setTimeout(() => {
      setIsProcessingImage(false);
      setImageUploadError(t('Image loading timed out. Please check the URL and try again.') || 'Image loading timed out. Please check the URL and try again.');
      showStatus('error', t('Image loading timed out') || 'Image loading timed out');
    }, 10000); // 10 second timeout

    testImage.onload = () => {
      clearTimeout(timeout);

      // Validate image dimensions
      if (testImage.width === 0 || testImage.height === 0) {
        setIsProcessingImage(false);
        setImageUploadError(t('Invalid image file. Please check the URL.') || 'Invalid image file. Please check the URL.');
        showStatus('error', t('Invalid image file') || 'Invalid image file');
        return;
      }

      setImagePreview(finalUrl);
      setProductForm(prev => ({ ...prev, image: finalUrl }));
      setImageUrl(finalUrl); // Update the URL field with the final URL
      setIsProcessingImage(false);
      showStatus('success', t('Image loaded successfully') || 'Image loaded successfully', 2000);
    };

    testImage.onerror = () => {
      clearTimeout(timeout);
      setIsProcessingImage(false);
      setImageUploadError(t('Failed to load image from URL. Please check the URL and try again.') || 'Failed to load image from URL. Please check the URL and try again.');
      showStatus('error', t('Failed to load image from URL') || 'Failed to load image from URL');
    };

    // Set crossOrigin to handle CORS issues
    testImage.crossOrigin = 'anonymous';
    testImage.src = finalUrl;
  };

  // Camera functionality
  const startCameraStream = async () => {
    try {
      setImageUploadError('');
      setIsProcessingImage(true);
      setIsCameraReady(false); // Reset camera ready state
      showStatus('info', t('Starting camera...') || 'Starting camera...');

      // Check if getUserMedia is supported
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('Camera not supported on this device');
      }

      // Try different camera configurations
      let stream = null;
      const constraints = [
        // Try back camera first (mobile)
        {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: { exact: 'environment' }
          }
        },
        // Fallback to any camera
        {
          video: {
            width: { ideal: 1280 },
            height: { ideal: 720 },
            facingMode: 'user'
          }
        },
        // Basic fallback
        {
          video: true
        }
      ];

      for (const constraint of constraints) {
        try {
          stream = await navigator.mediaDevices.getUserMedia(constraint);
          break;
        } catch (err) {
          console.log('Camera constraint failed:', constraint, err);
          continue;
        }
      }

      if (!stream) {
        throw new Error('Unable to access any camera');
      }

      streamRef.current = stream;
      console.log(`ProductTemplateBuilder - Video ref status: ${videoRef.current ? 'Available' : 'NULL'}`);

      // Set camera stream to active first to render the video element
      setShowCameraStream(true);

      // Wait a moment for the video element to be rendered
      await new Promise(resolve => setTimeout(resolve, 100));

      console.log(`ProductTemplateBuilder - Video ref status after delay: ${videoRef.current ? 'Available' : 'NULL'}`);

      if (videoRef.current) {
        videoRef.current.srcObject = stream;
        console.log('ProductTemplateBuilder - Video srcObject set, starting enhanced readiness detection...');

        // Enhanced approach: Start video playback immediately and monitor readiness
        const video = videoRef.current;

        // Start playing the video immediately
        try {
          await video.play();
          console.log('ProductTemplateBuilder - Video play() called successfully');
        } catch (playError) {
          console.log('ProductTemplateBuilder - Video play() failed:', playError.message, ', continuing with readiness checks...');
        }

        // Enhanced readiness detection with multiple strategies
        await new Promise((resolve, reject) => {
          let resolved = false;
          let checkCount = 0;
          const maxChecks = 100; // 10 seconds total

          const checkVideoReadiness = () => {
            if (resolved) return;
            checkCount++;

            const readyState = video.readyState;
            const width = video.videoWidth;
            const height = video.videoHeight;
            const paused = video.paused;
            const ended = video.ended;

            console.log(`ProductTemplateBuilder - Check #${checkCount}: readyState=${readyState}, width=${width}, height=${height}, paused=${paused}, ended=${ended}`);

            // Multiple readiness criteria - any of these should work
            const isReady = (
              // Primary criteria: full readiness
              (readyState >= 2 && width > 0 && height > 0 && !paused && !ended) ||
              // Secondary criteria: has dimensions and some data
              (readyState >= 1 && width > 0 && height > 0) ||
              // Fallback criteria: just has dimensions (for some browsers)
              (width > 0 && height > 0 && checkCount > 10)
            );

            if (isReady) {
              resolved = true;
              console.log(`ProductTemplateBuilder - Camera is ready! Criteria met: readyState=${readyState}, dimensions=${width}x${height}`);
              setIsCameraReady(true);
              setShowCameraReadyIndicator(true);

              setTimeout(() => {
                setShowCameraReadyIndicator(false);
              }, 3000);

              resolve();
            } else if (checkCount >= maxChecks) {
              // Final timeout - force resolution if video has any dimensions
              if (width > 0 && height > 0) {
                console.log(`ProductTemplateBuilder - Forcing camera ready after ${maxChecks} checks - video has dimensions ${width}x${height}`);
                resolved = true;
                setIsCameraReady(true);
                setShowCameraReadyIndicator(true);
                setTimeout(() => setShowCameraReadyIndicator(false), 3000);
                resolve();
              } else {
                console.log(`ProductTemplateBuilder - Camera readiness failed after ${maxChecks} checks - no video dimensions`);
                reject(new Error('Camera readiness timeout - no video dimensions detected'));
              }
            } else {
              // Continue checking
              setTimeout(checkVideoReadiness, 100);
            }
          };

          // Start checking immediately
          checkVideoReadiness();

          // Additional event listeners as backup
          const onLoadedData = () => {
            if (!resolved) {
              console.log('ProductTemplateBuilder - loadeddata event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onCanPlay = () => {
            if (!resolved) {
              console.log('ProductTemplateBuilder - canplay event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onLoadedMetadata = () => {
            if (!resolved) {
              console.log('ProductTemplateBuilder - loadedmetadata event fired, triggering readiness check');
              checkVideoReadiness();
            }
          };

          const onError = (error) => {
            if (!resolved) {
              resolved = true;
              console.log('ProductTemplateBuilder - Video error:', error.message || error);
              reject(error);
            }
          };

          video.addEventListener('loadeddata', onLoadedData);
          video.addEventListener('canplay', onCanPlay);
          video.addEventListener('loadedmetadata', onLoadedMetadata);
          video.addEventListener('error', onError);

          // Cleanup function
          const cleanup = () => {
            video.removeEventListener('loadeddata', onLoadedData);
            video.removeEventListener('canplay', onCanPlay);
            video.removeEventListener('loadedmetadata', onLoadedMetadata);
            video.removeEventListener('error', onError);
          };

          // Final timeout
          setTimeout(() => {
            if (!resolved) {
              cleanup();
              console.log('ProductTemplateBuilder - Final timeout reached - forcing resolution if possible');
              if (video.videoWidth > 0 && video.videoHeight > 0) {
                resolved = true;
                setIsCameraReady(true);
                setShowCameraReadyIndicator(true);
                setTimeout(() => setShowCameraReadyIndicator(false), 3000);
                resolve();
              } else {
                reject(new Error('Video loading timeout - no dimensions after 15 seconds'));
              }
            } else {
              cleanup();
            }
          }, 15000);
        });
      } else {
        console.log('ProductTemplateBuilder - ERROR: Video element not available even after delay!');
      }

      setIsProcessingImage(false);
      showStatus('success', t('Camera ready - click capture to take photo') || 'Camera ready - click capture to take photo', 3000);
    } catch (error) {
      console.error('Error accessing camera:', error);
      setIsProcessingImage(false);
      setIsCameraReady(false); // Ensure camera ready state is reset on error
      setShowCameraStream(false); // Hide camera stream on error

      let errorMessage = t('Unable to access camera') || 'Unable to access camera';

      if (error.name === 'NotAllowedError') {
        errorMessage = t('Camera access denied. Please allow camera access and try again.') || 'Camera access denied. Please allow camera access and try again.';
      } else if (error.name === 'NotFoundError') {
        errorMessage = t('No camera found on this device.') || 'No camera found on this device.';
      } else if (error.name === 'NotSupportedError' || error.message.includes('not supported')) {
        errorMessage = t('Camera not supported on this device.') || 'Camera not supported on this device.';
      } else if (error.name === 'OverconstrainedError') {
        errorMessage = t('Camera constraints not supported. Trying basic camera access...') || 'Camera constraints not supported. Trying basic camera access...';
      } else if (error.message.includes('timeout')) {
        errorMessage = t('Camera initialization timed out. Please try again.') || 'Camera initialization timed out. Please try again.';
      }

      setImageUploadError(errorMessage);
      showStatus('error', errorMessage);
    }
  };

  const stopCameraStream = () => {
    if (streamRef.current) {
      streamRef.current.getTracks().forEach(track => track.stop());
      streamRef.current = null;
    }
    setShowCameraStream(false);
    setIsCameraReady(false);
    setShowCameraReadyIndicator(false);
  };

  const capturePhoto = () => {
    if (!videoRef.current || !canvasRef.current) {
      showStatus('error', t('Camera not ready') || 'Camera not ready');
      return;
    }

    try {
      const video = videoRef.current;
      const canvas = canvasRef.current;
      const context = canvas.getContext('2d');

      // Enhanced video readiness check
      const checkVideoReady = () => {
        return video.readyState >= 2 && // HAVE_CURRENT_DATA or higher
               video.videoWidth > 0 &&
               video.videoHeight > 0 &&
               !video.paused &&
               !video.ended;
      };

      // If video is not ready, wait a bit and try again
      if (!checkVideoReady()) {
        showStatus('info', t('Preparing camera...') || 'Preparing camera...');

        // Wait for video to be ready with timeout
        const waitForVideo = new Promise((resolve, reject) => {
          let attempts = 0;
          const maxAttempts = 20; // 2 seconds total

          const checkInterval = setInterval(() => {
            attempts++;

            if (checkVideoReady()) {
              clearInterval(checkInterval);
              resolve();
            } else if (attempts >= maxAttempts) {
              clearInterval(checkInterval);
              reject(new Error('Video not ready after timeout'));
            }
          }, 100); // Check every 100ms
        });

        waitForVideo
          .then(() => {
            // Retry capture after video is ready
            setTimeout(() => capturePhoto(), 100);
          })
          .catch(() => {
            showStatus('error', t('Camera not responding. Please try restarting the camera.') || 'Camera not responding. Please try restarting the camera.');
          });

        return;
      }

      // Get actual video dimensions
      const videoWidth = video.videoWidth || video.clientWidth || 640;
      const videoHeight = video.videoHeight || video.clientHeight || 480;

      // Set canvas dimensions to match video
      canvas.width = videoWidth;
      canvas.height = videoHeight;

      // Clear canvas first
      context.clearRect(0, 0, canvas.width, canvas.height);

      // Draw the video frame to canvas
      context.drawImage(video, 0, 0, canvas.width, canvas.height);

      // Convert to base64 with good quality
      const base64Data = canvas.toDataURL('image/jpeg', 0.85);

      // Validate the captured image
      if (base64Data === 'data:,' || base64Data.length < 1000) {
        showStatus('error', t('Failed to capture photo. Please try again.') || 'Failed to capture photo. Please try again.');
        return;
      }

      setImagePreview(base64Data);
      setProductForm(prev => ({ ...prev, image: base64Data }));
      stopCameraStream();
      showStatus('success', t('Photo captured successfully') || 'Photo captured successfully', 2000);
    } catch (error) {
      console.error('Error capturing photo:', error);
      showStatus('error', t('Failed to capture photo') || 'Failed to capture photo');
    }
  };

  // Validate category name
  const validateCategoryName = (name) => {
    const trimmedName = name.trim();
    if (!trimmedName) {
      return t('Category name cannot be empty') || 'Category name cannot be empty';
    }
    if (trimmedName.length < 2) {
      return t('Category name must be at least 2 characters') || 'Category name must be at least 2 characters';
    }
    if (trimmedName.length > 50) {
      return t('Category name must be less than 50 characters') || 'Category name must be less than 50 characters';
    }
    // Check for invalid characters
    if (!/^[a-zA-Z0-9\s\-_&()]+$/.test(trimmedName)) {
      return t('Category name contains invalid characters') || 'Category name contains invalid characters';
    }
    // Check if category already exists
    const allCategories = getAllCategories();
    if (allCategories.some(cat => cat.name.toLowerCase() === trimmedName.toLowerCase())) {
      return t('Category already exists') || 'Category already exists';
    }
    return null;
  };

  // Validate subcategory name
  const validateSubcategoryName = (name, categoryName) => {
    const trimmedName = name.trim();
    if (!trimmedName) {
      return t('Subcategory name cannot be empty') || 'Subcategory name cannot be empty';
    }
    if (trimmedName.length < 2) {
      return t('Subcategory name must be at least 2 characters') || 'Subcategory name must be at least 2 characters';
    }
    if (trimmedName.length > 50) {
      return t('Subcategory name must be less than 50 characters') || 'Subcategory name must be less than 50 characters';
    }
    // Check for invalid characters
    if (!/^[a-zA-Z0-9\s\-_&()]+$/.test(trimmedName)) {
      return t('Subcategory name contains invalid characters') || 'Subcategory name contains invalid characters';
    }
    // Check if subcategory already exists in this category
    const allCategories = getAllCategories();
    const category = allCategories.find(cat => cat.name === categoryName);
    if (category && category.subcategories.includes(trimmedName)) {
      return t('Subcategory already exists in this category') || 'Subcategory already exists in this category';
    }
    return null;
  };

  // Handle new category creation
  const handleCreateNewCategory = async () => {
    const error = validateCategoryName(newCategoryName);
    if (error) {
      setCategoryCreationError(error);
      return;
    }

    setIsCreatingCategory(true);
    setCategoryCreationError('');

    try {
      const newCategory = addCustomCategory(newCategoryName.trim());
      if (newCategory) {
        // Set the new category as selected
        setProductForm(prev => ({
          ...prev,
          category: newCategory.name,
          subcategory: '' // Reset subcategory when category changes
        }));

        // Reset creation state
        setIsAddingNewCategory(false);
        setNewCategoryName('');

        showStatus('success', t('Category created successfully') || 'Category created successfully');
      } else {
        setCategoryCreationError(t('Failed to create category') || 'Failed to create category');
      }
    } catch (error) {
      console.error('Error creating category:', error);
      setCategoryCreationError(t('Error creating category') || 'Error creating category');
    } finally {
      setIsCreatingCategory(false);
    }
  };

  // Handle new subcategory creation
  const handleCreateNewSubcategory = async () => {
    const error = validateSubcategoryName(newSubcategoryName, productForm.category);
    if (error) {
      setSubcategoryCreationError(error);
      return;
    }

    setIsCreatingSubcategory(true);
    setSubcategoryCreationError('');

    try {
      addCustomSubcategory(productForm.category, newSubcategoryName.trim());

      // Set the new subcategory as selected
      setProductForm(prev => ({
        ...prev,
        subcategory: newSubcategoryName.trim()
      }));

      // Reset creation state
      setIsAddingNewSubcategory(false);
      setNewSubcategoryName('');

      showStatus('success', t('Subcategory created successfully') || 'Subcategory created successfully');
    } catch (error) {
      console.error('Error creating subcategory:', error);
      setSubcategoryCreationError(t('Error creating subcategory') || 'Error creating subcategory');
    } finally {
      setIsCreatingSubcategory(false);
    }
  };

  // Cancel category creation
  const handleCancelCategoryCreation = () => {
    setIsAddingNewCategory(false);
    setNewCategoryName('');
    setCategoryCreationError('');
  };

  // Cancel subcategory creation
  const handleCancelSubcategoryCreation = () => {
    setIsAddingNewSubcategory(false);
    setNewSubcategoryName('');
    setSubcategoryCreationError('');
  };

  // Check if product already exists in main catalog
  const checkProductExists = (name, category, subcategory) => {
    const allProducts = [...customProducts];
    return allProducts.some(product =>
      product.name.toLowerCase() === name.toLowerCase() &&
      product.category.toLowerCase() === category.toLowerCase() &&
      product.subcategory.toLowerCase() === subcategory.toLowerCase()
    );
  };

  // Find existing product in main catalog
  const findExistingProduct = (name, category, subcategory) => {
    const allProducts = [...customProducts];
    return allProducts.find(product =>
      product.name.toLowerCase() === name.toLowerCase() &&
      product.category.toLowerCase() === category.toLowerCase() &&
      product.subcategory.toLowerCase() === subcategory.toLowerCase()
    );
  };

  // Add or update product (standalone operation)
  const handleAddProduct = () => {
    if (!validateProductForm()) return;

    const productData = {
      name: productForm.name.trim(),
      category: productForm.category,
      subcategory: productForm.subcategory,
      price: parseFloat(productForm.price) || 0,
      quantity: parseInt(productForm.quantity) || 1,
      size: productForm.size.trim(),
      material: productForm.material.trim(),
      image: productForm.image
    };

    // For editing existing products, proceed directly
    if (editingProductIndex !== null) {
      const templateProduct = {
        id: `template-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
        ...productData,
        createdAt: new Date().toISOString()
      };

      const updatedProducts = [...templateProducts];
      updatedProducts[editingProductIndex] = templateProduct;
      setTemplateProducts(updatedProducts);
      showStatus('success', t('Product updated in template') || 'Product updated in template');
      resetProductForm();
      return;
    }

    // Check for duplicates within template only
    const existingInTemplate = templateProducts.find(product =>
      product.name.toLowerCase() === productData.name.toLowerCase() &&
      product.category.toLowerCase() === productData.category.toLowerCase() &&
      product.subcategory.toLowerCase() === productData.subcategory.toLowerCase()
    );

    if (existingInTemplate) {
      showStatus('error', t('Product with this name already exists in template') || 'Product with this name already exists in template');
      return;
    }

    // Add new product to template
    const templateProduct = {
      id: `template-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      ...productData,
      createdAt: new Date().toISOString()
    };

    setTemplateProducts(prev => [...prev, templateProduct]);
    showStatus('success', t('Product added to template') || 'Product added to template');
    resetProductForm();
  };

  // Proceed with product addition (with or without replacing existing)
  const proceedWithProductAddition = (productData, replaceExisting = false) => {
    let mainCatalogProduct = null;

    try {
      if (replaceExisting) {
        // Update existing product in main catalog
        const existingProduct = findExistingProduct(productData.name, productData.category, productData.subcategory);
        if (existingProduct) {
          // Update the existing product with new data
          const updatedData = { ...existingProduct, ...productData };
          updateCustomProduct(existingProduct.id, updatedData);
          mainCatalogProduct = updatedData;
          showStatus('success', t('Existing product updated in main catalog') || 'Existing product updated in main catalog');
        }
      } else {
        // Add new product to main catalog
        mainCatalogProduct = addCustomProduct(productData);
        showStatus('success', t('Product added to template and main catalog') || 'Product added to template and main catalog');
      }
    } catch (error) {
      console.error('Error managing product in main catalog:', error);
      showStatus('error', t('Error managing product in main catalog') || 'Error managing product in main catalog');
      return;
    }

    // Create template product with proper synchronization
    const templateProduct = {
      id: `template-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      ...productData,
      mainCatalogId: mainCatalogProduct?.id || null,
      syncedAt: new Date().toISOString()
    };

    // Add to template
    setTemplateProducts(prev => [...prev, templateProduct]);
    resetProductForm();
  };

  // Handle duplicate resolution actions
  const handleDuplicateResolution = (action) => {
    if (!pendingProductData || !duplicateConflict) return;

    switch (action) {
      case 'replace':
        proceedWithProductAddition(pendingProductData, true);
        showStatus('success', t('Product replaced in main catalog and added to template') || 'Product replaced in main catalog and added to template');
        break;

      case 'keep':
        // Add to template only, referencing existing product
        const templateProduct = {
          id: `template-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
          ...duplicateConflict.existing,
          mainCatalogId: duplicateConflict.existing.id
        };
        setTemplateProducts(prev => [...prev, templateProduct]);
        showStatus('info', t('Existing product added to template') || 'Existing product added to template');
        resetProductForm();
        break;

      case 'addBoth':
        // Create a variant of the new product to avoid exact duplication
        const variantProduct = {
          ...pendingProductData,
          name: `${pendingProductData.name} (Variant)`,
        };
        proceedWithProductAddition(variantProduct, false);
        showStatus('success', t('Both products added - new variant created in main catalog') || 'Both products added - new variant created in main catalog');
        break;

      case 'merge':
        // Merge the best attributes from both products
        const mergedProduct = {
          ...duplicateConflict.existing,
          // Use new data if it's more complete, otherwise keep existing
          price: (pendingProductData.price && pendingProductData.price > 0) ? pendingProductData.price : duplicateConflict.existing.price,
          size: pendingProductData.size?.trim() || duplicateConflict.existing.size,
          material: pendingProductData.material?.trim() || duplicateConflict.existing.material,
          image: pendingProductData.image || duplicateConflict.existing.image,
          // Update timestamp
          updatedAt: new Date().toISOString()
        };
        proceedWithProductAddition(mergedProduct, true);
        showStatus('success', t('Products merged and updated in main catalog') || 'Products merged and updated in main catalog');
        break;

      default:
        break;
    }

    // Close modal and reset state
    setShowDuplicateModal(false);
    setDuplicateConflict(null);
    setPendingProductData(null);
  };

  // Edit product
  const handleEditProduct = (index) => {
    const product = templateProducts[index];
    setProductForm({
      name: product.name,
      category: product.category,
      subcategory: product.subcategory,
      price: product.price.toString(),
      quantity: (product.quantity || 1).toString(),
      size: product.size,
      material: product.material,
      image: product.image
    });
    setImagePreview(product.image);
    setImageUrl(product.image && !product.image.startsWith('data:') ? product.image : '');
    setImageInputMethod(product.image && product.image.startsWith('data:') ? 'upload' : 'url');
    setImageUploadError('');
    setEditingProductIndex(index);
  };

  // Delete product (standalone operation)
  const handleDeleteProduct = (index) => {
    // Remove from template
    const updatedProducts = templateProducts.filter((_, i) => i !== index);
    setTemplateProducts(updatedProducts);

    if (editingProductIndex === index) {
      resetProductForm();
    }

    showStatus('success', t('Product deleted successfully') || 'Product deleted successfully');
  };

  // Clear all products (standalone operation)
  const handleClearAllProducts = () => {
    if (templateProducts.length === 0) return;

    const shouldClear = window.confirm(
      t('This will clear all products from the template. Are you sure?') ||
      'This will clear all products from the template. Are you sure?'
    );

    if (shouldClear) {
      // Clear template
      setTemplateProducts([]);
      resetProductForm();
      localStorage.removeItem('plomdesign-template-builder');
      showStatus('success', t('All products cleared') || 'All products cleared');
    }
  };

  // Save template and update main product catalog
  const handleSaveTemplate = () => {
    if (templateProducts.length === 0) {
      showStatus('error', t('No products in template to save') || 'No products in template to save');
      return;
    }

    const shouldSave = window.confirm(
      t('This will add new products and update existing products from your template. Existing products not in the template will remain unchanged. Are you sure?') ||
      'This will add new products and update existing products from your template. Existing products not in the template will remain unchanged. Are you sure?'
    );

    if (shouldSave) {
      try {
        const result = replaceProductCatalogWithTemplate(templateProducts);

        if (result.success) {
          const { addedCount, updatedCount, totalProcessed } = result;
          let successMessage = '';

          if (addedCount > 0 && updatedCount > 0) {
            successMessage = t('Product catalog updated successfully! {{added}} products added, {{updated}} products updated.',
              { added: addedCount, updated: updatedCount }) ||
              `Product catalog updated successfully! ${addedCount} products added, ${updatedCount} products updated.`;
          } else if (addedCount > 0) {
            successMessage = t('Product catalog updated successfully! {{added}} new products added.',
              { added: addedCount }) ||
              `Product catalog updated successfully! ${addedCount} new products added.`;
          } else if (updatedCount > 0) {
            successMessage = t('Product catalog updated successfully! {{updated}} products updated.',
              { updated: updatedCount }) ||
              `Product catalog updated successfully! ${updatedCount} products updated.`;
          } else {
            successMessage = t('Template processed successfully! No changes were needed.') ||
              'Template processed successfully! No changes were needed.';
          }

          showStatus('success', successMessage);

          // Clear template after successful save
          setTemplateProducts([]);
          resetProductForm();
          localStorage.removeItem('plomdesign-template-builder');

          // Close the modal after a short delay
          setTimeout(() => {
            onClose();
          }, 2000);
        } else {
          showStatus('error',
            t('Failed to save template: {{error}}', { error: result.error }) ||
            `Failed to save template: ${result.error}`
          );
        }
      } catch (error) {
        console.error('Error saving template:', error);
        showStatus('error', t('Error saving template') || 'Error saving template');
      }
    }
  };

  // Validate template data
  const validateTemplateData = () => {
    const issues = [];

    templateProducts.forEach((product, index) => {
      // Check for missing required fields
      if (!product.name || !product.category || !product.subcategory) {
        issues.push({
          type: 'incomplete',
          product,
          index,
          message: t('Product has missing required fields') || 'Product has missing required fields'
        });
      }
    });

    return issues;
  };

  if (!isOpen) return null;

  const allCategories = getAllCategories();
  const selectedCategory = allCategories.find(cat => cat.name === productForm.category);
  const availableSubcategories = selectedCategory ? selectedCategory.subcategories : [];

  return (
    <div className="fixed inset-0 bg-gradient-to-br from-slate-900/80 via-gray-900/80 to-indigo-900/80 backdrop-blur-sm flex items-center justify-center z-50">
      <div className="bg-white rounded-2xl shadow-2xl w-full max-w-7xl h-[90vh] flex flex-col overflow-hidden border border-gray-200/50">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200/60 bg-gradient-to-r from-indigo-50 via-blue-50 to-purple-50 relative">
          <div className="absolute inset-0 bg-gradient-to-r from-indigo-500/5 via-blue-500/5 to-purple-500/5"></div>
          <div className="relative z-10">
            <h1 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 via-blue-600 to-purple-600 bg-clip-text text-transparent flex items-center">
              <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl mr-3 shadow-lg">
                <Package className="w-6 h-6 text-white" />
              </div>
              {t('productTemplateBuilder') || 'Product Template Builder'}
            </h1>
            <p className="text-sm text-slate-600 mt-2 font-medium">
              {t('templateBuilderDescription') || 'Create custom product templates with images for easy Excel import'}
            </p>
          </div>
          <button
            onClick={() => {
              onClose();
              resetProductForm();
            }}
            className="relative z-10 p-2 hover:bg-white/60 rounded-xl transition-all duration-200 hover:shadow-md group"
          >
            <X size={24} className="text-slate-500 group-hover:text-slate-700" />
          </button>
        </div>

        {/* Status Messages */}
        {statusMessage && (
          <div className={`mx-6 mt-4 p-4 rounded-xl flex items-center shadow-sm border backdrop-blur-sm ${
            statusMessage.type === 'success'
              ? 'bg-gradient-to-r from-emerald-50 to-green-50 border-emerald-200/60 text-emerald-800'
              : statusMessage.type === 'info'
              ? 'bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200/60 text-blue-800'
              : 'bg-gradient-to-r from-red-50 to-rose-50 border-red-200/60 text-red-800'
          }`}>
            <div className={`p-1 rounded-lg mr-3 ${
              statusMessage.type === 'success'
                ? 'bg-emerald-100'
                : statusMessage.type === 'info'
                ? 'bg-blue-100'
                : 'bg-red-100'
            }`}>
              {statusMessage.type === 'success' && <CheckCircle size={16} className="text-emerald-600" />}
              {statusMessage.type === 'info' && <Info size={16} className="text-blue-600" />}
              {statusMessage.type === 'error' && <AlertCircle size={16} className="text-red-600" />}
            </div>
            <span className="font-medium">{statusMessage.message}</span>
          </div>
        )}

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Panel - Product Form */}
          <div className="w-2/5 p-6 border-r border-gray-200/60 overflow-y-auto bg-gradient-to-br from-slate-50 via-gray-50 to-blue-50/30">
            <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-6 shadow-lg border border-white/50">
              <h2 className="text-lg font-bold text-slate-800 mb-6 flex items-center">
                {editingProductIndex !== null ? (
                  <>
                    <div className="p-2 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-xl mr-3 shadow-md">
                      <Edit size={16} className="text-white" />
                    </div>
                    <span className="bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent">
                      {t('editProduct') || 'Edit Product'}
                    </span>
                  </>
                ) : (
                  <>
                    <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl mr-3 shadow-md">
                      <Plus size={16} className="text-white" />
                    </div>
                    <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                      {t('addProduct') || 'Add Product'}
                    </span>
                  </>
                )}
              </h2>

              <div className="space-y-4">
                {/* Product Name */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('productName') || 'Product Name'} <span className="text-rose-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={productForm.name}
                    onChange={(e) => setProductForm(prev => ({ ...prev, name: e.target.value }))}
                    className={`w-full p-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 bg-white/70 backdrop-blur-sm text-gray-900 ${
                      formErrors.name
                        ? 'border-rose-300 focus:ring-rose-500/30 bg-rose-50/70'
                        : 'border-gray-300/60 focus:ring-indigo-500/30 hover:border-indigo-300'
                    }`}
                    style={{
                      color: '#111827',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                    placeholder={t('enterProductName') || 'Enter product name'}
                  />
                  {formErrors.name && (
                    <p className="text-rose-600 text-xs mt-2 flex items-center font-medium">
                      <AlertCircle size={12} className="mr-1" />
                      {formErrors.name}
                    </p>
                  )}
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('Category') || 'Category'} <span className="text-rose-500">*</span>
                  </label>

                  {!isAddingNewCategory ? (
                    <select
                      value={productForm.category}
                      onChange={(e) => {
                        if (e.target.value === '__ADD_NEW__') {
                          setIsAddingNewCategory(true);
                        } else {
                          setProductForm(prev => ({ ...prev, category: e.target.value, subcategory: '' }));
                        }
                      }}
                      className={`w-full p-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 bg-white/70 backdrop-blur-sm text-gray-900 ${
                        formErrors.category
                          ? 'border-rose-300 focus:ring-rose-500/30 bg-rose-50/70'
                          : 'border-gray-300/60 focus:ring-indigo-500/30 hover:border-indigo-300'
                      }`}
                      style={{
                        color: '#111827',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)'
                      }}
                    >
                      <option value="" style={{ color: '#6b7280', backgroundColor: '#ffffff' }}>
                        {t('selectCategory') || 'Select Category'}
                      </option>
                      {allCategories.map(category => (
                        <option key={category.name} value={category.name} style={{ color: '#111827', backgroundColor: '#ffffff' }}>
                          {category.name}
                        </option>
                      ))}
                      <option value="__ADD_NEW__" style={{ color: '#059669', backgroundColor: '#f0fdf4', fontWeight: 'bold' }}>
                        ➕ {t('Add New Category') || 'Add New Category'}
                      </option>
                    </select>
                  ) : (
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={newCategoryName}
                          onChange={(e) => {
                            setNewCategoryName(e.target.value);
                            setCategoryCreationError('');
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && newCategoryName.trim() && !isCreatingCategory) {
                              handleCreateNewCategory();
                            } else if (e.key === 'Escape') {
                              handleCancelCategoryCreation();
                            }
                          }}
                          placeholder={t('Enter new category name') || 'Enter new category name'}
                          className="flex-1 p-3 border border-emerald-300/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500/30 transition-all duration-200 bg-white/70 backdrop-blur-sm text-gray-900"
                          style={{
                            color: '#111827',
                            backgroundColor: 'rgba(255, 255, 255, 0.7)'
                          }}
                          disabled={isCreatingCategory}
                          autoFocus
                        />
                        <button
                          onClick={handleCreateNewCategory}
                          disabled={isCreatingCategory || !newCategoryName.trim()}
                          className="px-4 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-xl hover:from-emerald-700 hover:to-green-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 font-bold shadow-md flex items-center"
                        >
                          {isCreatingCategory ? (
                            <RefreshCw size={16} className="animate-spin" />
                          ) : (
                            <CheckCircle size={16} />
                          )}
                        </button>
                        <button
                          onClick={handleCancelCategoryCreation}
                          disabled={isCreatingCategory}
                          className="px-4 py-3 bg-gradient-to-r from-slate-500 to-gray-600 text-white rounded-xl hover:from-slate-600 hover:to-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-bold shadow-md"
                        >
                          <X size={16} />
                        </button>
                      </div>
                      {categoryCreationError && (
                        <p className="text-rose-600 text-xs flex items-center font-medium">
                          <AlertCircle size={12} className="mr-1" />
                          {categoryCreationError}
                        </p>
                      )}
                    </div>
                  )}

                  {formErrors.category && (
                    <p className="text-rose-600 text-xs mt-2 flex items-center font-medium">
                      <AlertCircle size={12} className="mr-1" />
                      {formErrors.category}
                    </p>
                  )}
                </div>

                {/* Subcategory */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('Subcategory') || 'Subcategory'} <span className="text-rose-500">*</span>
                  </label>

                  {!isAddingNewSubcategory ? (
                    <select
                      value={productForm.subcategory}
                      onChange={(e) => {
                        if (e.target.value === '__ADD_NEW__') {
                          setIsAddingNewSubcategory(true);
                        } else {
                          setProductForm(prev => ({ ...prev, subcategory: e.target.value }));
                        }
                      }}
                      className={`w-full p-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 bg-white/70 backdrop-blur-sm text-gray-900 ${
                        formErrors.subcategory
                          ? 'border-rose-300 focus:ring-rose-500/30 bg-rose-50/70'
                          : 'border-gray-300/60 focus:ring-indigo-500/30 hover:border-indigo-300'
                      } ${!productForm.category ? 'opacity-60 cursor-not-allowed' : ''}`}
                      disabled={!productForm.category}
                      style={{
                        color: !productForm.category ? '#9ca3af' : '#111827',
                        backgroundColor: 'rgba(255, 255, 255, 0.7)'
                      }}
                    >
                      <option value="" style={{ color: '#6b7280', backgroundColor: '#ffffff' }}>
                        {t('selectSubcategory') || 'Select Subcategory'}
                      </option>
                      {availableSubcategories.map(subcategory => (
                        <option key={subcategory} value={subcategory} style={{ color: '#111827', backgroundColor: '#ffffff' }}>
                          {subcategory}
                        </option>
                      ))}
                      {productForm.category && (
                        <option value="__ADD_NEW__" style={{ color: '#059669', backgroundColor: '#f0fdf4', fontWeight: 'bold' }}>
                          ➕ {t('Add New Subcategory') || 'Add New Subcategory'}
                        </option>
                      )}
                    </select>
                  ) : (
                    <div className="space-y-3">
                      <div className="flex gap-2">
                        <input
                          type="text"
                          value={newSubcategoryName}
                          onChange={(e) => {
                            setNewSubcategoryName(e.target.value);
                            setSubcategoryCreationError('');
                          }}
                          onKeyDown={(e) => {
                            if (e.key === 'Enter' && newSubcategoryName.trim() && !isCreatingSubcategory) {
                              handleCreateNewSubcategory();
                            } else if (e.key === 'Escape') {
                              handleCancelSubcategoryCreation();
                            }
                          }}
                          placeholder={t('Enter new subcategory name') || 'Enter new subcategory name'}
                          className="flex-1 p-3 border border-emerald-300/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500/30 transition-all duration-200 bg-white/70 backdrop-blur-sm text-gray-900"
                          style={{
                            color: '#111827',
                            backgroundColor: 'rgba(255, 255, 255, 0.7)'
                          }}
                          disabled={isCreatingSubcategory}
                          autoFocus
                        />
                        <button
                          onClick={handleCreateNewSubcategory}
                          disabled={isCreatingSubcategory || !newSubcategoryName.trim()}
                          className="px-4 py-3 bg-gradient-to-r from-emerald-600 to-green-600 text-white rounded-xl hover:from-emerald-700 hover:to-green-700 disabled:from-gray-400 disabled:to-gray-500 disabled:cursor-not-allowed transition-all duration-200 font-bold shadow-md flex items-center"
                        >
                          {isCreatingSubcategory ? (
                            <RefreshCw size={16} className="animate-spin" />
                          ) : (
                            <CheckCircle size={16} />
                          )}
                        </button>
                        <button
                          onClick={handleCancelSubcategoryCreation}
                          disabled={isCreatingSubcategory}
                          className="px-4 py-3 bg-gradient-to-r from-slate-500 to-gray-600 text-white rounded-xl hover:from-slate-600 hover:to-gray-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200 font-bold shadow-md"
                        >
                          <X size={16} />
                        </button>
                      </div>
                      {subcategoryCreationError && (
                        <p className="text-rose-600 text-xs flex items-center font-medium">
                          <AlertCircle size={12} className="mr-1" />
                          {subcategoryCreationError}
                        </p>
                      )}
                      <p className="text-xs text-slate-600 font-medium">
                        {t('Adding to category') || 'Adding to category'}: <span className="font-bold text-emerald-700">{productForm.category}</span>
                      </p>
                    </div>
                  )}

                  {formErrors.subcategory && (
                    <p className="text-rose-600 text-xs mt-2 flex items-center font-medium">
                      <AlertCircle size={12} className="mr-1" />
                      {formErrors.subcategory}
                    </p>
                  )}
                </div>

                {/* Price */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('Price') || 'Price'} <span className="text-slate-400 font-normal">({t('optionalField') || 'Optional'})</span>
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={productForm.price}
                    onChange={(e) => setProductForm(prev => ({ ...prev, price: e.target.value }))}
                    className="w-full p-3 border border-gray-300/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-emerald-500/30 transition-all duration-200 bg-white/70 backdrop-blur-sm hover:border-emerald-300 text-gray-900"
                    style={{
                      color: '#111827',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                    placeholder={t('enterPrice') || 'Enter price'}
                  />
                </div>

                {/* Quantity */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('Quantity') || 'Quantity'} <span className="text-rose-500">*</span>
                  </label>
                  <input
                    type="number"
                    min="1"
                    step="1"
                    value={productForm.quantity}
                    onChange={(e) => setProductForm(prev => ({ ...prev, quantity: e.target.value }))}
                    className={`w-full p-3 border rounded-xl focus:outline-none focus:ring-2 transition-all duration-200 bg-white/70 backdrop-blur-sm text-gray-900 ${
                      formErrors.quantity
                        ? 'border-rose-300 focus:ring-rose-500/30 bg-rose-50/70'
                        : 'border-gray-300/60 focus:ring-orange-500/30 hover:border-orange-300'
                    }`}
                    style={{
                      color: '#111827',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                    placeholder={t('enterQuantity') || 'Enter quantity'}
                  />
                  {formErrors.quantity && (
                    <p className="text-rose-600 text-xs mt-2 flex items-center font-medium">
                      <AlertCircle size={12} className="mr-1" />
                      {formErrors.quantity}
                    </p>
                  )}
                </div>

                {/* Size */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('Size') || 'Size/Diameter'} <span className="text-slate-400 font-normal">({t('optionalField') || 'Optional'})</span>
                  </label>
                  <input
                    type="text"
                    value={productForm.size}
                    onChange={(e) => setProductForm(prev => ({ ...prev, size: e.target.value }))}
                    className="w-full p-3 border border-gray-300/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-blue-500/30 transition-all duration-200 bg-white/70 backdrop-blur-sm hover:border-blue-300 text-gray-900"
                    style={{
                      color: '#111827',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                    placeholder={t('enterSize') || 'Enter size or diameter'}
                  />
                </div>

                {/* Material */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-2">
                    {t('Material') || 'Material'} <span className="text-slate-400 font-normal">({t('optionalField') || 'Optional'})</span>
                  </label>
                  <input
                    type="text"
                    value={productForm.material}
                    onChange={(e) => setProductForm(prev => ({ ...prev, material: e.target.value }))}
                    className="w-full p-3 border border-gray-300/60 rounded-xl focus:outline-none focus:ring-2 focus:ring-purple-500/30 transition-all duration-200 bg-white/70 backdrop-blur-sm hover:border-purple-300 text-gray-900"
                    style={{
                      color: '#111827',
                      backgroundColor: 'rgba(255, 255, 255, 0.7)'
                    }}
                    placeholder={t('enterMaterial') || 'Enter material'}
                  />
                </div>

                {/* Enhanced Image Upload */}
                <div>
                  <label className="block text-sm font-semibold text-slate-700 mb-3">
                    {t('Image') || 'Product Image'} <span className="text-slate-400 font-normal">({t('optionalField') || 'Optional'})</span>
                  </label>

                  {/* Image Upload Error */}
                  {imageUploadError && (
                    <div className="mb-3 p-3 rounded-xl bg-red-50 border border-red-200 text-red-800">
                      <p className="text-sm font-medium">{imageUploadError}</p>
                    </div>
                  )}

                  {/* Image Preview */}
                  {imagePreview && (
                    <div className="mb-4 p-4 border border-gray-200 rounded-xl bg-gray-50">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <img
                            src={imagePreview}
                            alt={t('imagePreview') || 'Image Preview'}
                            className="w-24 h-24 object-cover border-2 border-white rounded-xl shadow-lg"
                            onError={(e) => {
                              e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iOTYiIGhlaWdodD0iOTYiIHZpZXdCb3g9IjAgMCA5NiA5NiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9Ijk2IiBoZWlnaHQ9Ijk2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0zMiA0MEw0OCA1Nkw2NCA0MEw3MiA0OEw3MiA2NEgyNFY0OEwzMiA0MFoiIGZpbGw9IiM5Q0EzQUYiLz4KPGNpcmNsZSBjeD0iNDAiIGN5PSI0MCIgcj0iNCIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 mb-1">
                            {t('Current Image') || 'Current Image'}
                          </p>
                          <p className="text-xs text-gray-500 break-all">
                            {imagePreview.startsWith('data:')
                              ? t('Uploaded file') || 'Uploaded file'
                              : imagePreview
                            }
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={removeImage}
                          className="flex-shrink-0 p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded"
                          title={t('removeImage') || 'Remove image'}
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Input Method Selection */}
                  <div className="mb-4">
                    <div className="flex space-x-1 bg-gray-100 rounded-lg p-1">
                      <button
                        type="button"
                        onClick={() => setImageInputMethod('upload')}
                        className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                          imageInputMethod === 'upload'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <Upload size={14} className="inline mr-1" />
                        {t('Upload') || 'Upload'}
                      </button>
                      <button
                        type="button"
                        onClick={() => setImageInputMethod('camera')}
                        className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                          imageInputMethod === 'camera'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <Camera size={14} className="inline mr-1" />
                        {t('Camera') || 'Camera'}
                      </button>
                      <button
                        type="button"
                        onClick={() => setImageInputMethod('url')}
                        className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-all ${
                          imageInputMethod === 'url'
                            ? 'bg-white text-gray-900 shadow-sm'
                            : 'text-gray-600 hover:text-gray-900'
                        }`}
                      >
                        <Link size={14} className="inline mr-1" />
                        {t('URL') || 'URL'}
                      </button>
                    </div>
                  </div>

                  {/* Upload Method */}
                  {imageInputMethod === 'upload' && (
                    <div className="space-y-3">
                      <label className={`flex flex-col items-center justify-center w-full h-32 border-2 border-dashed rounded-xl cursor-pointer transition-all duration-200 ${
                        isProcessingImage
                          ? 'border-gray-300 bg-gray-100 cursor-not-allowed'
                          : 'border-gray-300 bg-gray-50 hover:bg-gray-100 hover:border-gray-400'
                      }`}>
                        <div className="flex flex-col items-center justify-center pt-5 pb-6">
                          <Upload className={`w-8 h-8 mb-2 ${isProcessingImage ? 'text-gray-400' : 'text-gray-500'}`} />
                          <p className={`mb-2 text-sm ${isProcessingImage ? 'text-gray-400' : 'text-gray-500'}`}>
                            <span className="font-semibold">
                              {isProcessingImage ? t('Processing...') || 'Processing...' : t('Click to upload') || 'Click to upload'}
                            </span>
                          </p>
                          <p className={`text-xs ${isProcessingImage ? 'text-gray-400' : 'text-gray-500'}`}>
                            {t('PNG, JPG, JPEG, GIF, WebP, SVG (max 2MB)') || 'PNG, JPG, JPEG, GIF, WebP, SVG (max 2MB)'}
                          </p>
                        </div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={(e) => processImageFile(e.target.files[0])}
                          className="hidden"
                          disabled={isProcessingImage}
                        />
                      </label>
                    </div>
                  )}

                  {/* Camera Method */}
                  {imageInputMethod === 'camera' && (
                    <div className="space-y-3">
                      {!showCameraStream ? (
                        <div>
                          <button
                            type="button"
                            onClick={startCameraStream}
                            disabled={isProcessingImage}
                            className={`w-full flex items-center justify-center px-4 py-3 rounded-xl font-medium transition-all duration-200 ${
                              isProcessingImage
                                ? 'bg-gray-400 text-white cursor-not-allowed'
                                : 'bg-blue-600 text-white hover:bg-blue-700 hover:shadow-lg'
                            }`}
                          >
                            <Camera size={16} className="mr-2" />
                            {isProcessingImage ? t('Starting camera...') || 'Starting camera...' : t('Start Camera') || 'Start Camera'}
                          </button>

                          {/* Retry button for camera issues */}
                          {imageUploadError && imageUploadError.toLowerCase().includes('camera') && (
                            <button
                              type="button"
                              onClick={() => {
                                setImageUploadError('');
                                startCameraStream();
                              }}
                              className="w-full mt-2 flex items-center justify-center px-4 py-2 rounded-lg font-medium bg-gray-600 text-white hover:bg-gray-700 transition-colors"
                            >
                              {t('Retry Camera') || 'Retry Camera'}
                            </button>
                          )}
                        </div>
                      ) : (
                        <div className="space-y-3">
                          <div className="relative bg-black rounded-xl overflow-hidden">
                            <video
                              ref={videoRef}
                              autoPlay
                              playsInline
                              className="w-full h-64 object-cover"
                            />
                            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-3">
                              <button
                                type="button"
                                onClick={capturePhoto}
                                disabled={!isCameraReady}
                                className={`px-4 py-2 rounded-lg font-medium transition-colors ${
                                  isCameraReady
                                    ? 'bg-white text-gray-900 hover:bg-gray-100'
                                    : 'bg-gray-400 text-gray-600 cursor-not-allowed'
                                }`}
                              >
                                <Camera size={16} className="inline mr-1" />
                                {isCameraReady ? t('Capture') || 'Capture' : t('Preparing...') || 'Preparing...'}
                              </button>
                              <button
                                type="button"
                                onClick={stopCameraStream}
                                className="px-4 py-2 bg-red-600 text-white rounded-lg font-medium hover:bg-red-700 transition-colors"
                              >
                                <X size={16} className="inline mr-1" />
                                {t('Cancel') || 'Cancel'}
                              </button>
                            </div>

                            {/* Camera status indicator */}
                            {!isCameraReady && (
                              <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                                <div className="bg-black bg-opacity-70 text-white px-3 py-1 rounded-lg text-sm flex items-center">
                                  <div className="animate-spin rounded-full h-3 w-3 border-b-2 border-white mr-2"></div>
                                  {t('Initializing camera...') || 'Initializing camera...'}
                                </div>
                              </div>
                            )}

                            {/* Camera ready indicator */}
                            {isCameraReady && showCameraReadyIndicator && (
                              <div className="absolute top-4 left-1/2 transform -translate-x-1/2">
                                <div className="bg-green-600 bg-opacity-90 text-white px-3 py-1 rounded-lg text-sm flex items-center">
                                  <div className="w-2 h-2 bg-green-300 rounded-full mr-2"></div>
                                  {t('Camera ready') || 'Camera ready'}
                                </div>
                              </div>
                            )}
                          </div>
                          <canvas ref={canvasRef} className="hidden" />
                        </div>
                      )}
                    </div>
                  )}

                  {/* URL Method */}
                  {imageInputMethod === 'url' && (
                    <div className="space-y-3">
                      <div className="flex space-x-2">
                        <input
                          type="url"
                          value={imageUrl}
                          onChange={(e) => setImageUrl(e.target.value)}
                          onKeyPress={(e) => {
                            if (e.key === 'Enter' && !isProcessingImage && imageUrl.trim()) {
                              e.preventDefault();
                              handleImageUrlInput();
                            }
                          }}
                          placeholder={t('Enter image URL...') || 'Enter image URL...'}
                          className="flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-gray-900"
                          disabled={isProcessingImage}
                        />
                        <button
                          type="button"
                          onClick={handleImageUrlInput}
                          disabled={isProcessingImage || !imageUrl.trim()}
                          className={`px-4 py-2 rounded-lg font-medium transition-all duration-200 ${
                            isProcessingImage || !imageUrl.trim()
                              ? 'bg-gray-400 text-white cursor-not-allowed'
                              : 'bg-blue-600 text-white hover:bg-blue-700'
                          }`}
                        >
                          {isProcessingImage ? t('Loading...') || 'Loading...' : t('Load') || 'Load'}
                        </button>
                      </div>
                      <p className="text-xs text-gray-500">
                        {t('Enter a valid image URL (https://example.com/image.jpg)') || 'Enter a valid image URL (https://example.com/image.jpg)'}
                      </p>
                    </div>
                  )}
                </div>

                {/* Action Buttons */}
                <div className="flex gap-3 pt-6 border-t border-gray-200/60">
                  <button
                    onClick={handleAddProduct}
                    className="flex-1 px-5 py-3 bg-gradient-to-r from-indigo-600 to-purple-600 text-white rounded-xl hover:from-indigo-700 hover:to-purple-700 hover:shadow-lg transition-all duration-200 font-bold shadow-md"
                  >
                    {editingProductIndex !== null ? t('Update Product') || 'Update Product' : t('addProduct') || 'Add Product'}
                  </button>
                  {editingProductIndex !== null && (
                    <button
                      onClick={resetProductForm}
                      className="px-5 py-3 bg-gradient-to-r from-slate-500 to-gray-600 text-white rounded-xl hover:from-slate-600 hover:to-gray-700 hover:shadow-lg transition-all duration-200 font-bold shadow-md"
                    >
                      {t('Cancel') || 'Cancel'}
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Right Panel - Products List */}
          <div className="w-3/5 p-6 overflow-y-auto bg-gradient-to-br from-white via-slate-50/30 to-blue-50/20">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-xl font-bold text-slate-800 flex items-center">
                <div className="p-2 bg-gradient-to-br from-emerald-500 to-teal-600 rounded-xl mr-3 shadow-md">
                  <FileSpreadsheet size={20} className="text-white" />
                </div>
                <span className="bg-gradient-to-r from-emerald-600 to-teal-600 bg-clip-text text-transparent">
                  {t('productsInTemplate') || 'Products in Template'}
                </span>
                <span className="ml-2 px-3 py-1 bg-gradient-to-r from-slate-100 to-gray-100 text-slate-700 rounded-full text-sm font-bold shadow-sm">
                  {templateProducts.length}
                </span>
              </h2>
              {templateProducts.length > 0 && (
                <div className="flex gap-3">
                  <button
                    onClick={handleSaveTemplate}
                    className="px-4 py-2 bg-gradient-to-r from-green-600 to-emerald-600 text-white rounded-xl hover:from-green-700 hover:to-emerald-700 hover:shadow-lg transition-all duration-200 font-bold shadow-md flex items-center"
                    title={t('Save template and update product catalog') || 'Save template and update product catalog'}
                  >
                    <Save size={16} className="mr-2" />
                    {t('Save & Update Catalog') || 'Save & Update Catalog'}
                  </button>
                  <button
                    onClick={handleClearAllProducts}
                    className="px-4 py-2 bg-gradient-to-r from-red-600 to-rose-600 text-white rounded-xl hover:from-red-700 hover:to-rose-700 hover:shadow-lg transition-all duration-200 font-bold shadow-md"
                  >
                    {t('clearAll') || 'Clear All'}
                  </button>
                </div>
              )}
            </div>

            {templateProducts.length === 0 ? (
              <div className="text-center py-16 bg-gradient-to-br from-slate-50 to-gray-100 rounded-2xl border-2 border-dashed border-slate-300/60 shadow-inner">
                <div className="p-4 bg-gradient-to-br from-slate-200 to-gray-300 rounded-2xl inline-block mb-6 shadow-lg">
                  <Package size={48} className="text-slate-500" />
                </div>
                <h3 className="text-xl font-bold text-slate-700 mb-3">
                  {t('noProductsAdded') || 'No products added to template yet'}
                </h3>
                <p className="text-slate-600 mb-8 font-medium">
                  {t('addFirstProduct') || 'Add your first product using the form on the left'}
                </p>
                <div className="text-sm text-slate-500 space-y-2 font-medium">
                  <p className="flex items-center justify-center">
                    <span className="w-2 h-2 bg-emerald-500 rounded-full mr-3"></span>
                    {t('Fill in product details') || 'Fill in product details'}
                  </p>
                  <p className="flex items-center justify-center">
                    <span className="w-2 h-2 bg-blue-500 rounded-full mr-3"></span>
                    {t('Upload product images') || 'Upload product images'}
                  </p>
                  <p className="flex items-center justify-center">
                    <span className="w-2 h-2 bg-purple-500 rounded-full mr-3"></span>
                    {t('Export as Excel template') || 'Export as Excel template'}
                  </p>
                </div>
              </div>
            ) : (
              <div className="space-y-5">
                {templateProducts.map((product, index) => (
                  <div
                    key={product.id}
                    className={`border rounded-2xl p-6 transition-all duration-300 hover:shadow-xl backdrop-blur-sm transform hover:-translate-y-1 ${
                      editingProductIndex === index
                        ? 'bg-gradient-to-br from-indigo-50 via-white to-purple-50 border-indigo-300/70 shadow-xl ring-2 ring-indigo-300/40 scale-[1.02]'
                        : 'bg-gradient-to-br from-white via-slate-50/30 to-gray-50/20 border-gray-200/70 hover:border-indigo-300/50 hover:bg-gradient-to-br hover:from-white hover:via-indigo-50/20 hover:to-purple-50/10 shadow-md hover:shadow-2xl'
                    }`}
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-start gap-4">
                          {/* Product Image */}
                          <div className="w-20 h-20 bg-gradient-to-br from-slate-100 via-gray-100 to-slate-200 border-2 border-white rounded-2xl flex-shrink-0 flex items-center justify-center overflow-hidden shadow-lg ring-2 ring-gray-200/40 hover:ring-indigo-200/60 transition-all duration-300">
                            {product.image ? (
                              <img
                                src={product.image}
                                alt={product.name}
                                className="object-cover w-full h-full hover:scale-110 transition-transform duration-300"
                              />
                            ) : (
                              <div className="p-3 bg-gradient-to-br from-slate-200 to-gray-300 rounded-xl">
                                <Package size={32} className="text-slate-500" />
                              </div>
                            )}
                          </div>

                          {/* Product Details */}
                          <div className="flex-1 min-w-0">
                            <h3 className="font-bold text-slate-900 text-xl mb-3 truncate leading-tight">
                              {product.name}
                            </h3>
                            <div className="mb-4 p-2 bg-gradient-to-r from-emerald-50 to-teal-50 rounded-lg border border-emerald-100/60">
                              <p className="text-sm font-medium">
                                <span className="font-bold bg-gradient-to-r from-emerald-700 to-teal-700 bg-clip-text text-transparent">{product.category}</span>
                                <span className="mx-2 text-slate-400 font-bold">›</span>
                                <span className="font-semibold text-slate-700">{product.subcategory}</span>
                              </p>
                            </div>
                            <div className="flex flex-wrap items-center gap-3 text-sm mb-4">
                              {product.price > 0 && (
                                <span className="flex items-center px-3 py-2 bg-gradient-to-r from-emerald-50 to-green-50 rounded-xl border border-emerald-200/50 shadow-sm">
                                  <span className="text-slate-700 mr-2 font-semibold text-xs">{t('Price') || 'Price'}:</span>
                                  <span className="font-bold text-emerald-800 text-sm">{product.price.toFixed(2)}</span>
                                </span>
                              )}
                              {product.size && (
                                <span className="flex items-center px-3 py-2 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl border border-blue-200/50 shadow-sm">
                                  <span className="text-slate-700 mr-2 font-semibold text-xs">{t('Size') || 'Size'}:</span>
                                  <span className="font-bold text-blue-800 text-sm">{product.size}</span>
                                </span>
                              )}
                              {product.material && (
                                <span className="flex items-center px-3 py-2 bg-gradient-to-r from-purple-50 to-violet-50 rounded-xl border border-purple-200/50 shadow-sm">
                                  <span className="text-slate-700 mr-2 font-semibold text-xs">{t('Material') || 'Material'}:</span>
                                  <span className="font-bold text-purple-800 text-sm">{product.material}</span>
                                </span>
                              )}
                            </div>

                            {/* Product Status */}
                            <div className="flex flex-wrap items-center gap-2 mt-3">
                              <span className="flex items-center px-2 py-1 bg-gradient-to-r from-blue-50 to-indigo-50 rounded-lg border border-blue-200/50 shadow-sm">
                                <Package size={12} className="text-blue-600 mr-1" />
                                <span className="text-blue-700 text-xs font-semibold">
                                  {t('Template Product') || 'Template Product'}
                                </span>
                              </span>
                              {product.createdAt && (
                                <span className="flex items-center px-2 py-1 bg-gradient-to-r from-gray-50 to-slate-50 rounded-lg border border-gray-200/50 shadow-sm">
                                  <span className="text-gray-700 text-xs font-semibold">
                                    {t('Added') || 'Added'}: {new Date(product.createdAt).toLocaleDateString()}
                                  </span>
                                </span>
                              )}
                            </div>

                          </div>
                        </div>
                      </div>

                      {/* Action Buttons */}
                      <div className="flex gap-2 ml-4">
                        <button
                          onClick={() => handleEditProduct(index)}
                          className="p-3 bg-gradient-to-br from-indigo-50 to-purple-50 text-indigo-600 hover:from-indigo-100 hover:to-purple-100 hover:text-indigo-700 rounded-xl transition-all duration-200 hover:shadow-md border border-indigo-200/50"
                          title={t('editProduct') || 'Edit Product'}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => handleDeleteProduct(index)}
                          className="p-3 bg-gradient-to-br from-red-50 to-rose-50 text-red-600 hover:from-red-100 hover:to-rose-100 hover:text-red-700 rounded-xl transition-all duration-200 hover:shadow-md border border-red-200/50"
                          title={t('deleteProduct') || 'Delete Product'}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>
                  </div>
                ))}

                {/* Template Summary */}
                <div className="mt-6 p-6 bg-gradient-to-br from-white via-slate-50 to-indigo-50/30 rounded-2xl border border-gray-200/60 shadow-lg backdrop-blur-sm">
                  <h4 className="font-bold text-slate-800 mb-5 flex items-center">
                    <div className="p-2 bg-gradient-to-br from-slate-500 to-gray-600 rounded-xl mr-3 shadow-md">
                      <FileSpreadsheet size={16} className="text-white" />
                    </div>
                    <span className="bg-gradient-to-r from-slate-700 to-gray-700 bg-clip-text text-transparent">
                      {t('Template Summary') || 'Template Summary'}
                    </span>
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div className="flex justify-between items-center p-3 bg-white/60 rounded-xl border border-gray-200/40">
                      <span className="text-slate-600 font-semibold">{t('Total Products') || 'Total Products'}:</span>
                      <span className="font-bold text-slate-800 text-lg">{templateProducts.length}</span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-blue-50/60 rounded-xl border border-blue-200/40">
                      <span className="text-slate-600 font-semibold">{t('Products with Images') || 'Products with Images'}:</span>
                      <span className="font-bold text-blue-700 text-lg">
                        {templateProducts.filter(p => p.image).length}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-purple-50/60 rounded-xl border border-purple-200/40">
                      <span className="text-slate-600 font-semibold">{t('Categories') || 'Categories'}:</span>
                      <span className="font-bold text-purple-700 text-lg">
                        {new Set(templateProducts.map(p => p.category)).size}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-emerald-50/60 rounded-xl border border-emerald-200/40">
                      <span className="text-slate-600 font-semibold">{t('Average Price') || 'Average Price'}:</span>
                      <span className="font-bold text-emerald-700 text-lg">
                        {templateProducts.length > 0
                          ? (templateProducts.reduce((sum, p) => sum + p.price, 0) / templateProducts.length).toFixed(2)
                          : '0.00'
                        }
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-indigo-50/60 rounded-xl border border-indigo-200/40">
                      <span className="text-slate-600 font-semibold">{t('Total Value') || 'Total Value'}:</span>
                      <span className="font-bold text-indigo-700 text-lg">
                        ${templateProducts.reduce((sum, p) => sum + p.price, 0).toFixed(2)}
                      </span>
                    </div>
                    <div className="flex justify-between items-center p-3 bg-orange-50/60 rounded-xl border border-orange-200/40">
                      <span className="text-slate-600 font-semibold">{t('Subcategories') || 'Subcategories'}:</span>
                      <span className="font-bold text-orange-700 text-lg">
                        {new Set(templateProducts.map(p => `${p.category}-${p.subcategory}`)).size}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Duplicate Detection Modal */}
      {showDuplicateModal && duplicateConflict && (
        <div className="fixed inset-0 bg-black/60 backdrop-blur-sm flex items-center justify-center z-[60]">
          <div className="bg-white rounded-2xl shadow-2xl w-full max-w-5xl max-h-[90vh] overflow-hidden border border-gray-200/50">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200/60 bg-gradient-to-r from-amber-50 via-orange-50 to-red-50">
              <div className="flex items-center">
                <div className="p-2 bg-gradient-to-br from-amber-500 to-orange-600 rounded-xl mr-3 shadow-lg">
                  <AlertCircle className="w-6 h-6 text-white" />
                </div>
                <div>
                  <h2 className="text-xl font-bold text-slate-800">
                    {t('Duplicate Product Detected') || 'Duplicate Product Detected'}
                  </h2>
                  <p className="text-sm text-slate-600 mt-1">
                    {t('A product with the same name, category, and subcategory already exists') || 'A product with the same name, category, and subcategory already exists'}
                  </p>
                </div>
              </div>
              <button
                onClick={() => setShowDuplicateModal(false)}
                className="p-2 hover:bg-white/60 rounded-xl transition-all duration-200 hover:shadow-md group"
              >
                <X size={24} className="text-slate-500 group-hover:text-slate-700" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
              {/* Product Comparison */}
              <div className="grid grid-cols-2 gap-6 mb-8">
                {/* Existing Product */}
                <div className="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-2xl p-6 border border-blue-200/50">
                  <h3 className="text-lg font-bold text-blue-800 mb-4 flex items-center">
                    <div className="p-2 bg-blue-500 rounded-xl mr-3">
                      <Package size={16} className="text-white" />
                    </div>
                    {t('Existing Product') || 'Existing Product'}
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      {duplicateConflict.existing.image ? (
                        <img
                          src={duplicateConflict.existing.image}
                          alt={duplicateConflict.existing.name}
                          className="w-16 h-16 object-cover rounded-xl border-2 border-white shadow-md"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gradient-to-br from-slate-200 to-gray-300 rounded-xl flex items-center justify-center">
                          <Package size={24} className="text-slate-500" />
                        </div>
                      )}
                      <div>
                        <h4 className="font-bold text-slate-800">{duplicateConflict.existing.name}</h4>
                        <p className="text-sm text-slate-600">
                          {duplicateConflict.existing.category} › {duplicateConflict.existing.subcategory}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="bg-white/60 rounded-lg p-3">
                        <span className="text-slate-600 font-medium">{t('Price') || 'Price'}:</span>
                        <span className="font-bold text-emerald-700 ml-2">
                          ${duplicateConflict.existing.price?.toFixed(2) || '0.00'}
                        </span>
                      </div>
                      <div className="bg-white/60 rounded-lg p-3">
                        <span className="text-slate-600 font-medium">{t('Size') || 'Size'}:</span>
                        <span className="font-bold text-blue-700 ml-2">
                          {duplicateConflict.existing.size || t('Not specified') || 'Not specified'}
                        </span>
                      </div>
                      <div className="bg-white/60 rounded-lg p-3 col-span-2">
                        <span className="text-slate-600 font-medium">{t('Material') || 'Material'}:</span>
                        <span className="font-bold text-purple-700 ml-2">
                          {duplicateConflict.existing.material || t('Not specified') || 'Not specified'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* New Product */}
                <div className="bg-gradient-to-br from-emerald-50 to-green-50 rounded-2xl p-6 border border-emerald-200/50">
                  <h3 className="text-lg font-bold text-emerald-800 mb-4 flex items-center">
                    <div className="p-2 bg-emerald-500 rounded-xl mr-3">
                      <Plus size={16} className="text-white" />
                    </div>
                    {t('New Product') || 'New Product'}
                  </h3>
                  <div className="space-y-3">
                    <div className="flex items-center gap-3">
                      {duplicateConflict.new.image ? (
                        <img
                          src={duplicateConflict.new.image}
                          alt={duplicateConflict.new.name}
                          className="w-16 h-16 object-cover rounded-xl border-2 border-white shadow-md"
                        />
                      ) : (
                        <div className="w-16 h-16 bg-gradient-to-br from-slate-200 to-gray-300 rounded-xl flex items-center justify-center">
                          <Package size={24} className="text-slate-500" />
                        </div>
                      )}
                      <div>
                        <h4 className="font-bold text-slate-800">{duplicateConflict.new.name}</h4>
                        <p className="text-sm text-slate-600">
                          {duplicateConflict.new.category} › {duplicateConflict.new.subcategory}
                        </p>
                      </div>
                    </div>
                    <div className="grid grid-cols-2 gap-3 text-sm">
                      <div className="bg-white/60 rounded-lg p-3">
                        <span className="text-slate-600 font-medium">{t('Price') || 'Price'}:</span>
                        <span className="font-bold text-emerald-700 ml-2">
                          ${duplicateConflict.new.price?.toFixed(2) || '0.00'}
                        </span>
                      </div>
                      <div className="bg-white/60 rounded-lg p-3">
                        <span className="text-slate-600 font-medium">{t('Size') || 'Size'}:</span>
                        <span className="font-bold text-blue-700 ml-2">
                          {duplicateConflict.new.size || t('Not specified') || 'Not specified'}
                        </span>
                      </div>
                      <div className="bg-white/60 rounded-lg p-3 col-span-2">
                        <span className="text-slate-600 font-medium">{t('Material') || 'Material'}:</span>
                        <span className="font-bold text-purple-700 ml-2">
                          {duplicateConflict.new.material || t('Not specified') || 'Not specified'}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Action Options */}
              <div className="space-y-4">
                <h3 className="text-lg font-bold text-slate-800 mb-4">
                  {t('Choose an action') || 'Choose an action'}:
                </h3>

                <div className="grid grid-cols-2 gap-4">
                  {/* Replace Option */}
                  <button
                    onClick={() => handleDuplicateResolution('replace')}
                    className="p-4 bg-gradient-to-br from-red-50 to-rose-50 border border-red-200/50 rounded-xl hover:from-red-100 hover:to-rose-100 transition-all duration-200 text-left group hover:shadow-lg"
                  >
                    <div className="flex items-center mb-2">
                      <div className="p-2 bg-red-500 rounded-lg mr-3 group-hover:bg-red-600 transition-colors">
                        <RefreshCw size={16} className="text-white" />
                      </div>
                      <span className="font-bold text-red-800">{t('Replace Existing') || 'Replace Existing'}</span>
                    </div>
                    <p className="text-sm text-red-700">
                      {t('Update the existing product with the new product data') || 'Update the existing product with the new product data'}
                    </p>
                  </button>

                  {/* Keep Existing Option */}
                  <button
                    onClick={() => handleDuplicateResolution('keep')}
                    className="p-4 bg-gradient-to-br from-blue-50 to-indigo-50 border border-blue-200/50 rounded-xl hover:from-blue-100 hover:to-indigo-100 transition-all duration-200 text-left group hover:shadow-lg"
                  >
                    <div className="flex items-center mb-2">
                      <div className="p-2 bg-blue-500 rounded-lg mr-3 group-hover:bg-blue-600 transition-colors">
                        <CheckCircle size={16} className="text-white" />
                      </div>
                      <span className="font-bold text-blue-800">{t('Keep Existing') || 'Keep Existing'}</span>
                    </div>
                    <p className="text-sm text-blue-700">
                      {t('Add the existing product to template and cancel the new addition') || 'Add the existing product to template and cancel the new addition'}
                    </p>
                  </button>

                  {/* Add Both Option */}
                  <button
                    onClick={() => handleDuplicateResolution('addBoth')}
                    className="p-4 bg-gradient-to-br from-purple-50 to-violet-50 border border-purple-200/50 rounded-xl hover:from-purple-100 hover:to-violet-100 transition-all duration-200 text-left group hover:shadow-lg"
                  >
                    <div className="flex items-center mb-2">
                      <div className="p-2 bg-purple-500 rounded-lg mr-3 group-hover:bg-purple-600 transition-colors">
                        <Copy size={16} className="text-white" />
                      </div>
                      <span className="font-bold text-purple-800">{t('Add Both') || 'Add Both'}</span>
                    </div>
                    <p className="text-sm text-purple-700">
                      {t('Create a new product variant and keep both in the catalog') || 'Create a new product variant and keep both in the catalog'}
                    </p>
                  </button>

                  {/* Merge Option */}
                  <button
                    onClick={() => handleDuplicateResolution('merge')}
                    className="p-4 bg-gradient-to-br from-emerald-50 to-green-50 border border-emerald-200/50 rounded-xl hover:from-emerald-100 hover:to-green-100 transition-all duration-200 text-left group hover:shadow-lg"
                  >
                    <div className="flex items-center mb-2">
                      <div className="p-2 bg-emerald-500 rounded-lg mr-3 group-hover:bg-emerald-600 transition-colors">
                        <Merge size={16} className="text-white" />
                      </div>
                      <span className="font-bold text-emerald-800">{t('Merge Products') || 'Merge Products'}</span>
                    </div>
                    <p className="text-sm text-emerald-700">
                      {t('Combine the best attributes from both products') || 'Combine the best attributes from both products'}
                    </p>
                  </button>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex justify-end gap-3 p-6 border-t border-gray-200/60 bg-gray-50/50">
              <button
                onClick={() => setShowDuplicateModal(false)}
                className="px-6 py-3 bg-gradient-to-r from-slate-500 to-gray-600 text-white rounded-xl hover:from-slate-600 hover:to-gray-700 hover:shadow-lg transition-all duration-200 font-bold shadow-md"
              >
                {t('Cancel') || 'Cancel'}
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ProductTemplateBuilder;
