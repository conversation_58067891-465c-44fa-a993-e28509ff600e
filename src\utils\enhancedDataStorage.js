/**
 * Enhanced Data Storage System for PlombDesign
 * Integrates SQLite database with existing localStorage system
 * Provides automatic migration and fallback capabilities
 */

import { sqliteManager } from './sqliteManager';
import { dataStorage, STORAGE_KEYS, DEFAULT_DATA } from './dataStorage';

export class EnhancedDataStorageManager {
  constructor() {
    this.sqliteEnabled = false;
    this.migrationCompleted = false;
    this.fallbackToLocalStorage = true;
  }

  /**
   * Initialize the enhanced storage system
   */
  async initialize() {
    try {
      console.log('Initializing Enhanced Data Storage System...');

      // Try to initialize SQLite
      const sqliteResult = await sqliteManager.initialize();
      
      if (sqliteResult.success) {
        this.sqliteEnabled = true;
        console.log('SQLite database initialized successfully');
        
        // Check if migration is needed
        await this.checkAndMigrate();
      } else {
        console.warn('SQLite initialization failed, falling back to localStorage:', sqliteResult.message);
        this.sqliteEnabled = false;
      }

      return {
        success: true,
        sqliteEnabled: this.sqliteEnabled,
        message: this.sqliteEnabled ? 'Enhanced storage with SQLite ready' : 'Using localStorage fallback'
      };

    } catch (error) {
      console.error('Error initializing enhanced storage:', error);
      this.sqliteEnabled = false;
      return {
        success: false,
        sqliteEnabled: false,
        message: `Initialization failed: ${error.message}`
      };
    }
  }

  /**
   * Check if migration from localStorage to SQLite is needed
   */
  async checkAndMigrate() {
    try {
      if (!this.sqliteEnabled) return;

      // Check if we have data in localStorage but not in SQLite
      const localStorageData = dataStorage.loadData(STORAGE_KEYS.CANVAS_STATE);
      const sqliteStats = await sqliteManager.getDatabaseStats();

      const hasLocalData = localStorageData && (
        (localStorageData.canvasProducts && localStorageData.canvasProducts.length > 0) ||
        (localStorageData.customProducts && localStorageData.customProducts.length > 0)
      );

      const hasSqliteData = sqliteStats.success && sqliteStats.stats.totalProducts > 0;

      if (hasLocalData && !hasSqliteData) {
        console.log('Migration needed: Found localStorage data but no SQLite data');
        await this.migrateFromLocalStorage();
      }

      this.migrationCompleted = true;

    } catch (error) {
      console.error('Error during migration check:', error);
    }
  }

  /**
   * Migrate data from localStorage to SQLite
   */
  async migrateFromLocalStorage() {
    try {
      console.log('Starting migration from localStorage to SQLite...');

      // Load all localStorage data
      const canvasState = dataStorage.loadData(STORAGE_KEYS.CANVAS_STATE, {});
      const customProducts = dataStorage.loadData(STORAGE_KEYS.PRODUCTS, []);

      let migratedCount = 0;
      const errors = [];

      // Migrate custom products
      if (customProducts && customProducts.length > 0) {
        console.log(`Migrating ${customProducts.length} custom products...`);

        for (const product of customProducts) {
          try {
            const productData = {
              category: product.category || 'Imported',
              subcategory: product.subcategory || 'Custom',
              product_name: product.name || `Product ${Date.now()}`,
              image_data: product.image || null,
              material: product.material || '',
              diameter: product.diameter || product.size || '',
              size: product.size || product.diameter || '',
              price: parseFloat(product.price) || 0,
              quantity: parseInt(product.quantity) || 0,
              metadata: {
                originalId: product.id,
                migratedFrom: 'localStorage',
                migratedAt: new Date().toISOString(),
                isCustom: true
              }
            };

            const result = await sqliteManager.insertOrReplaceProduct(productData);
            if (result.success) {
              migratedCount++;
            } else {
              errors.push({ product: product.name, error: result.message });
            }

          } catch (error) {
            errors.push({ product: product.name || 'Unknown', error: error.message });
          }
        }
      }

      // Migrate canvas products if they contain custom data
      if (canvasState.canvasProducts && canvasState.canvasProducts.length > 0) {
        const customCanvasProducts = canvasState.canvasProducts.filter(p => p.isCustom);
        
        if (customCanvasProducts.length > 0) {
          console.log(`Migrating ${customCanvasProducts.length} custom canvas products...`);

          for (const product of customCanvasProducts) {
            try {
              const productData = {
                category: product.category || 'Canvas',
                subcategory: product.subcategory || 'Custom',
                product_name: product.name || `Canvas Product ${Date.now()}`,
                image_data: product.image || null,
                material: product.material || '',
                diameter: product.diameter || product.size || '',
                size: product.size || product.diameter || '',
                price: parseFloat(product.price) || 0,
                quantity: 1,
                metadata: {
                  originalId: product.id,
                  migratedFrom: 'canvas',
                  migratedAt: new Date().toISOString(),
                  position: product.position,
                  rotation: product.rotation
                }
              };

              const result = await sqliteManager.insertOrReplaceProduct(productData);
              if (result.success) {
                migratedCount++;
              } else {
                errors.push({ product: product.name, error: result.message });
              }

            } catch (error) {
              errors.push({ product: product.name || 'Unknown', error: error.message });
            }
          }
        }
      }

      console.log(`Migration completed: ${migratedCount} products migrated, ${errors.length} errors`);

      if (errors.length > 0) {
        console.warn('Migration errors:', errors);
      }

      return {
        success: true,
        migratedCount,
        errors,
        message: `Successfully migrated ${migratedCount} products`
      };

    } catch (error) {
      console.error('Error during migration:', error);
      return {
        success: false,
        message: `Migration failed: ${error.message}`
      };
    }
  }

  /**
   * Save product with automatic storage selection
   */
  async saveProduct(productData) {
    try {
      // Ensure backward compatibility by adding quantity field if missing
      const enhancedProductData = {
        ...productData,
        quantity: typeof productData.quantity === 'number' ? productData.quantity : parseInt(productData.quantity) || 1
      };

      if (this.sqliteEnabled) {
        // Use SQLite as primary storage
        const result = await sqliteManager.insertOrReplaceProduct(enhancedProductData);

        if (result.success) {
          // Also save to localStorage as backup
          if (this.fallbackToLocalStorage) {
            this.saveToLocalStorageBackup(enhancedProductData);
          }
          return result;
        } else {
          // Fallback to localStorage if SQLite fails
          console.warn('SQLite save failed, falling back to localStorage');
          return this.saveToLocalStorage(enhancedProductData);
        }
      } else {
        // Use localStorage as primary storage
        return this.saveToLocalStorage(enhancedProductData);
      }

    } catch (error) {
      console.error('Error saving product:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Get products with automatic storage selection
   */
  async getProducts(filters = {}) {
    try {
      if (this.sqliteEnabled) {
        // Try SQLite first
        const result = await sqliteManager.getProducts(filters);

        if (result.success) {
          // Ensure backward compatibility for quantity field
          const enhancedProducts = result.products.map(product => ({
            ...product,
            quantity: typeof product.quantity === 'number' ? product.quantity : 1
          }));

          return {
            ...result,
            products: enhancedProducts
          };
        } else {
          // Fallback to localStorage
          console.warn('SQLite get failed, falling back to localStorage');
          return this.getFromLocalStorage(filters);
        }
      } else {
        // Use localStorage
        return this.getFromLocalStorage(filters);
      }

    } catch (error) {
      console.error('Error getting products:', error);
      return { success: false, message: error.message, products: [] };
    }
  }

  /**
   * Search products with enhanced capabilities
   */
  async searchProducts(searchTerm, options = {}) {
    try {
      if (this.sqliteEnabled) {
        return await sqliteManager.searchProducts(searchTerm, options);
      } else {
        // Implement basic search for localStorage
        return this.searchInLocalStorage(searchTerm, options);
      }

    } catch (error) {
      console.error('Error searching products:', error);
      return { success: false, message: error.message, products: [] };
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    try {
      const stats = {
        sqliteEnabled: this.sqliteEnabled,
        migrationCompleted: this.migrationCompleted,
        fallbackEnabled: this.fallbackToLocalStorage
      };

      if (this.sqliteEnabled) {
        const sqliteStats = await sqliteManager.getDatabaseStats();
        if (sqliteStats.success) {
          stats.sqlite = sqliteStats.stats;
        }
      }

      // Always include localStorage stats
      const localStorageInfo = dataStorage.getStorageInfo();
      stats.localStorage = localStorageInfo;

      return { success: true, stats };

    } catch (error) {
      console.error('Error getting storage stats:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Export all data
   */
  async exportAllData() {
    try {
      if (this.sqliteEnabled) {
        const sqliteExport = await sqliteManager.exportToJSON();
        if (sqliteExport.success) {
          return sqliteExport;
        }
      }

      // Fallback to localStorage export
      const canvasState = dataStorage.loadData(STORAGE_KEYS.CANVAS_STATE, {});
      const customProducts = dataStorage.loadData(STORAGE_KEYS.PRODUCTS, []);
      const settings = dataStorage.loadData(STORAGE_KEYS.SETTINGS, DEFAULT_DATA.settings);

      const exportData = {
        exportInfo: {
          timestamp: new Date().toISOString(),
          version: '1.0.0',
          application: 'PlombDesign Enhanced',
          source: 'localStorage'
        },
        canvasState,
        customProducts,
        settings
      };

      return { success: true, data: exportData };

    } catch (error) {
      console.error('Error exporting data:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Save to localStorage (fallback method)
   */
  saveToLocalStorage(productData) {
    try {
      // Convert to localStorage format
      const product = {
        id: `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
        name: productData.product_name,
        category: productData.category,
        subcategory: productData.subcategory,
        image: productData.image_data,
        material: productData.material,
        diameter: productData.diameter,
        size: productData.size,
        price: productData.price,
        quantity: productData.quantity,
        isCustom: true,
        createdAt: new Date().toISOString()
      };

      // Get existing products
      const existingProducts = dataStorage.loadData(STORAGE_KEYS.PRODUCTS, []);
      
      // Check for duplicates
      const duplicateIndex = existingProducts.findIndex(p => 
        p.name === product.name && 
        p.category === product.category && 
        p.subcategory === product.subcategory
      );

      if (duplicateIndex >= 0) {
        // Replace existing
        existingProducts[duplicateIndex] = product;
      } else {
        // Add new
        existingProducts.push(product);
      }

      // Save back to localStorage
      const saved = dataStorage.saveData(STORAGE_KEYS.PRODUCTS, existingProducts);

      return {
        success: saved,
        productId: product.id,
        message: saved ? 'Product saved to localStorage' : 'Failed to save to localStorage'
      };

    } catch (error) {
      console.error('Error saving to localStorage:', error);
      return { success: false, message: error.message };
    }
  }

  /**
   * Save to localStorage as backup
   */
  saveToLocalStorageBackup(productData) {
    try {
      // This is a simplified backup - just store the essential data
      const backupKey = `plomb_backup_product_${Date.now()}`;
      localStorage.setItem(backupKey, JSON.stringify({
        ...productData,
        backupTimestamp: new Date().toISOString()
      }));
    } catch (error) {
      console.warn('Failed to create localStorage backup:', error);
    }
  }

  /**
   * Get products from localStorage
   */
  getFromLocalStorage(filters = {}) {
    try {
      const products = dataStorage.loadData(STORAGE_KEYS.PRODUCTS, []);

      // Ensure backward compatibility for quantity field
      const enhancedProducts = products.map(product => ({
        ...product,
        quantity: typeof product.quantity === 'number' ? product.quantity : 1
      }));

      let filteredProducts = enhancedProducts;

      // Apply filters
      if (filters.category) {
        filteredProducts = filteredProducts.filter(p => p.category === filters.category);
      }
      if (filters.subcategory) {
        filteredProducts = filteredProducts.filter(p => p.subcategory === filters.subcategory);
      }
      if (filters.search) {
        const searchLower = filters.search.toLowerCase();
        filteredProducts = filteredProducts.filter(p =>
          p.name.toLowerCase().includes(searchLower) ||
          (p.material && p.material.toLowerCase().includes(searchLower))
        );
      }

      return { success: true, products: filteredProducts };

    } catch (error) {
      console.error('Error getting from localStorage:', error);
      return { success: false, message: error.message, products: [] };
    }
  }

  /**
   * Search in localStorage
   */
  searchInLocalStorage(searchTerm, options = {}) {
    try {
      const { limit = 100 } = options;
      const products = dataStorage.loadData(STORAGE_KEYS.PRODUCTS, []);

      // Ensure backward compatibility for quantity field
      const enhancedProducts = products.map(product => ({
        ...product,
        quantity: typeof product.quantity === 'number' ? product.quantity : 1
      }));

      const searchLower = searchTerm.toLowerCase();
      const results = enhancedProducts.filter(p =>
        p.name.toLowerCase().includes(searchLower) ||
        (p.material && p.material.toLowerCase().includes(searchLower)) ||
        (p.category && p.category.toLowerCase().includes(searchLower))
      ).slice(0, limit);

      return { success: true, products: results, count: results.length };

    } catch (error) {
      console.error('Error searching localStorage:', error);
      return { success: false, message: error.message, products: [] };
    }
  }
}

// Create singleton instance
export const enhancedDataStorage = new EnhancedDataStorageManager();
export default enhancedDataStorage;
