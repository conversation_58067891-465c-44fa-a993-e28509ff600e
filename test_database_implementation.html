<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PlombDesign Database Implementation Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background-color: #0056b3; }
        button:disabled { background-color: #6c757d; cursor: not-allowed; }
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 10px;
            margin: 10px 0;
        }
        .stat-card {
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 4px;
            text-align: center;
        }
        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #007bff;
        }
        .stat-label {
            font-size: 12px;
            color: #6c757d;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>PlombDesign Database Implementation Test</h1>
    
    <div class="container">
        <h2>System Status</h2>
        <div id="systemStatus" class="status info">Initializing...</div>
        
        <div class="stats" id="statsContainer">
            <div class="stat-card">
                <div class="stat-value" id="sqliteStatus">-</div>
                <div class="stat-label">SQLite Status</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="productCount">0</div>
                <div class="stat-label">Total Products</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="dbSize">0 KB</div>
                <div class="stat-label">Database Size</div>
            </div>
            <div class="stat-card">
                <div class="stat-value" id="migrationStatus">-</div>
                <div class="stat-label">Migration Status</div>
            </div>
        </div>
    </div>

    <div class="grid">
        <div class="container">
            <h3>Database Operations</h3>
            <button onclick="initializeDatabase()" id="initBtn">Initialize Database</button>
            <button onclick="addSampleProducts()" id="addBtn" disabled>Add Sample Products</button>
            <button onclick="searchProducts()" id="searchBtn" disabled>Search Products</button>
            <button onclick="getStats()" id="statsBtn" disabled>Refresh Stats</button>
            <button onclick="exportData()" id="exportBtn" disabled>Export Data</button>
            <button onclick="clearDatabase()" id="clearBtn" disabled>Clear Database</button>
        </div>

        <div class="container">
            <h3>Test Results</h3>
            <div id="testResults"></div>
        </div>
    </div>

    <div class="container">
        <h3>Operation Log</h3>
        <div id="operationLog" class="log"></div>
    </div>

    <script type="module">
        // Import the enhanced data storage system
        // Note: In a real implementation, these would be proper ES6 imports
        
        let enhancedStorage = null;
        let testResults = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logElement = document.getElementById('operationLog');
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}\n`;
            logElement.textContent += logEntry;
            logElement.scrollTop = logElement.scrollHeight;
            
            console.log(`[PlombDesign Test] ${message}`);
        }

        function updateStatus(message, type = 'info') {
            const statusElement = document.getElementById('systemStatus');
            statusElement.textContent = message;
            statusElement.className = `status ${type}`;
        }

        function addTestResult(test, success, message) {
            testResults.push({ test, success, message, timestamp: new Date() });
            updateTestResults();
        }

        function updateTestResults() {
            const resultsElement = document.getElementById('testResults');
            resultsElement.innerHTML = testResults.map(result => 
                `<div class="status ${result.success ? 'success' : 'error'}">
                    <strong>${result.test}</strong>: ${result.message}
                    <small style="display: block; margin-top: 5px;">${result.timestamp.toLocaleTimeString()}</small>
                </div>`
            ).join('');
        }

        function updateStats(stats = {}) {
            document.getElementById('sqliteStatus').textContent = stats.sqliteEnabled ? 'Enabled' : 'Disabled';
            document.getElementById('productCount').textContent = stats.totalProducts || 0;
            document.getElementById('dbSize').textContent = stats.formattedSize || '0 KB';
            document.getElementById('migrationStatus').textContent = stats.migrationStatus || 'Pending';
        }

        function enableButtons() {
            ['addBtn', 'searchBtn', 'statsBtn', 'exportBtn', 'clearBtn'].forEach(id => {
                document.getElementById(id).disabled = false;
            });
        }

        // Mock implementation for testing (since we can't import the actual modules in this test file)
        class MockEnhancedDataStorage {
            constructor() {
                this.initialized = false;
                this.products = [];
                this.stats = {
                    sqliteEnabled: false,
                    totalProducts: 0,
                    formattedSize: '0 KB',
                    migrationStatus: 'pending'
                };
            }

            async initialize() {
                log('Initializing enhanced data storage...');
                
                // Simulate initialization delay
                await new Promise(resolve => setTimeout(resolve, 1000));
                
                try {
                    // Check if sql.js is available (it won't be in this test, but we'll simulate)
                    this.initialized = true;
                    this.stats.sqliteEnabled = true;
                    this.stats.migrationStatus = 'completed';
                    
                    log('Enhanced data storage initialized successfully');
                    return { success: true, sqliteEnabled: true, message: 'Initialization successful' };
                } catch (error) {
                    log(`Initialization failed: ${error.message}`, 'error');
                    return { success: false, sqliteEnabled: false, message: error.message };
                }
            }

            async saveProduct(productData) {
                if (!this.initialized) {
                    throw new Error('Storage not initialized');
                }

                log(`Saving product: ${productData.product_name}`);
                
                // Simulate saving
                const product = {
                    id: Date.now(),
                    ...productData,
                    created_at: new Date().toISOString()
                };
                
                this.products.push(product);
                this.stats.totalProducts = this.products.length;
                this.stats.formattedSize = `${Math.round(this.products.length * 0.5)} KB`;
                
                return { success: true, productId: product.id, message: 'Product saved successfully' };
            }

            async getProducts(filters = {}) {
                if (!this.initialized) {
                    throw new Error('Storage not initialized');
                }

                let filteredProducts = [...this.products];
                
                if (filters.search) {
                    const searchTerm = filters.search.toLowerCase();
                    filteredProducts = filteredProducts.filter(p => 
                        p.product_name.toLowerCase().includes(searchTerm) ||
                        p.category.toLowerCase().includes(searchTerm)
                    );
                }

                return { success: true, products: filteredProducts };
            }

            async searchProducts(searchTerm, options = {}) {
                return this.getProducts({ search: searchTerm });
            }

            async getStorageStats() {
                return { success: true, stats: this.stats };
            }

            async exportAllData() {
                const exportData = {
                    exportInfo: {
                        timestamp: new Date().toISOString(),
                        version: '1.0.0',
                        application: 'PlombDesign Test'
                    },
                    products: this.products,
                    stats: this.stats
                };

                return { success: true, data: exportData };
            }

            async clearAllData() {
                this.products = [];
                this.stats.totalProducts = 0;
                this.stats.formattedSize = '0 KB';
                return { success: true, message: 'All data cleared' };
            }
        }

        // Initialize mock storage
        enhancedStorage = new MockEnhancedDataStorage();

        // Test functions
        window.initializeDatabase = async function() {
            try {
                updateStatus('Initializing database...', 'info');
                log('Starting database initialization test...');
                
                const result = await enhancedStorage.initialize();
                
                if (result.success) {
                    updateStatus('Database initialized successfully', 'success');
                    addTestResult('Database Initialization', true, 'SQLite database initialized and ready');
                    enableButtons();
                    
                    // Update stats
                    const statsResult = await enhancedStorage.getStorageStats();
                    if (statsResult.success) {
                        updateStats(statsResult.stats);
                    }
                } else {
                    updateStatus('Database initialization failed', 'error');
                    addTestResult('Database Initialization', false, result.message);
                }
            } catch (error) {
                updateStatus('Database initialization error', 'error');
                addTestResult('Database Initialization', false, error.message);
                log(`Initialization error: ${error.message}`, 'error');
            }
        };

        window.addSampleProducts = async function() {
            try {
                log('Adding sample products...');
                
                const sampleProducts = [
                    {
                        category: 'Pipes & Fittings',
                        subcategory: 'Copper Pipes',
                        product_name: 'Copper Pipe 1/2"',
                        material: 'Copper',
                        diameter: '1/2"',
                        price: 12.99,
                        quantity: 50
                    },
                    {
                        category: 'Valves & Controls',
                        subcategory: 'Ball Valves',
                        product_name: 'Ball Valve 3/4"',
                        material: 'Brass',
                        diameter: '3/4"',
                        price: 25.50,
                        quantity: 30
                    },
                    {
                        category: 'Fixtures & Appliances',
                        subcategory: 'Faucets',
                        product_name: 'Kitchen Faucet',
                        material: 'Stainless Steel',
                        diameter: 'Standard',
                        price: 89.99,
                        quantity: 15
                    }
                ];

                let successCount = 0;
                for (const product of sampleProducts) {
                    const result = await enhancedStorage.saveProduct(product);
                    if (result.success) {
                        successCount++;
                    }
                }

                addTestResult('Add Sample Products', true, `${successCount}/${sampleProducts.length} products added successfully`);
                
                // Update stats
                const statsResult = await enhancedStorage.getStorageStats();
                if (statsResult.success) {
                    updateStats(statsResult.stats);
                }
                
            } catch (error) {
                addTestResult('Add Sample Products', false, error.message);
                log(`Error adding products: ${error.message}`, 'error');
            }
        };

        window.searchProducts = async function() {
            try {
                log('Testing product search...');
                
                const searchResult = await enhancedStorage.searchProducts('copper');
                
                if (searchResult.success) {
                    addTestResult('Product Search', true, `Found ${searchResult.products.length} products matching 'copper'`);
                    log(`Search completed: ${searchResult.products.length} results`);
                } else {
                    addTestResult('Product Search', false, searchResult.message);
                }
                
            } catch (error) {
                addTestResult('Product Search', false, error.message);
                log(`Search error: ${error.message}`, 'error');
            }
        };

        window.getStats = async function() {
            try {
                log('Refreshing database statistics...');
                
                const statsResult = await enhancedStorage.getStorageStats();
                
                if (statsResult.success) {
                    updateStats(statsResult.stats);
                    addTestResult('Statistics Refresh', true, 'Database statistics updated successfully');
                } else {
                    addTestResult('Statistics Refresh', false, statsResult.message);
                }
                
            } catch (error) {
                addTestResult('Statistics Refresh', false, error.message);
                log(`Stats error: ${error.message}`, 'error');
            }
        };

        window.exportData = async function() {
            try {
                log('Testing data export...');
                
                const exportResult = await enhancedStorage.exportAllData();
                
                if (exportResult.success) {
                    // Create download
                    const blob = new Blob([JSON.stringify(exportResult.data, null, 2)], {
                        type: 'application/json'
                    });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = `plombdesign-test-export-${new Date().toISOString().split('T')[0]}.json`;
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    URL.revokeObjectURL(url);
                    
                    addTestResult('Data Export', true, `Exported ${exportResult.data.products.length} products`);
                } else {
                    addTestResult('Data Export', false, exportResult.message);
                }
                
            } catch (error) {
                addTestResult('Data Export', false, error.message);
                log(`Export error: ${error.message}`, 'error');
            }
        };

        window.clearDatabase = async function() {
            if (!confirm('Are you sure you want to clear all test data?')) {
                return;
            }
            
            try {
                log('Clearing database...');
                
                const clearResult = await enhancedStorage.clearAllData();
                
                if (clearResult.success) {
                    addTestResult('Clear Database', true, 'All test data cleared successfully');
                    updateStats({ sqliteEnabled: true, totalProducts: 0, formattedSize: '0 KB', migrationStatus: 'completed' });
                } else {
                    addTestResult('Clear Database', false, clearResult.message);
                }
                
            } catch (error) {
                addTestResult('Clear Database', false, error.message);
                log(`Clear error: ${error.message}`, 'error');
            }
        };

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('PlombDesign Database Implementation Test loaded');
            updateStatus('Ready to test - Click "Initialize Database" to begin', 'info');
        });
    </script>
</body>
</html>
