#!/usr/bin/env python3
"""
PlombDesign Data Storage System
SQLite-based data management for gigabyte-scale product databases
Implements the complete to-do list requirements
"""

import sqlite3
import os
import json
import logging
import base64
from datetime import datetime
from pathlib import Path
import hashlib

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('plombdesign.log'),
        logging.StreamHandler()
    ]
)

class PlombDesignDataStorage:
    def __init__(self, base_path="PlombDesign/Save Data"):
        """
        Initialize the PlombDesign data storage system
        
        Args:
            base_path (str): Base directory for data storage
        """
        self.base_path = Path(base_path)
        self.images_path = self.base_path / "Images"
        self.db_path = self.base_path / "plombdesign.db"
        
        # Create directories
        self.base_path.mkdir(parents=True, exist_ok=True)
        self.images_path.mkdir(exist_ok=True)
        
        # Initialize database
        self.conn = None
        self.initialize_database()
        
        logging.info(f"PlombDesign Data Storage initialized at: {self.base_path}")

    def initialize_database(self):
        """Initialize SQLite database and create tables"""
        try:
            self.conn = sqlite3.connect(str(self.db_path))
            self.conn.row_factory = sqlite3.Row  # Enable column access by name
            
            # Enable foreign keys
            self.conn.execute("PRAGMA foreign_keys = ON")
            
            self.create_tables()
            self.create_indexes()
            
            logging.info("Database initialized successfully")
            
        except Exception as e:
            logging.error(f"Database initialization failed: {e}")
            raise

    def create_tables(self):
        """Create database tables with proper schema"""
        
        # Products table with UNIQUE constraint for duplicate detection
        products_table = """
        CREATE TABLE IF NOT EXISTS products (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category TEXT NOT NULL,
            subcategory TEXT NOT NULL,
            product_name TEXT NOT NULL,
            image_path TEXT,
            material TEXT DEFAULT '',
            diameter TEXT DEFAULT '',
            price REAL DEFAULT 0.0,
            quantity INTEGER DEFAULT 0,
            last_updated DATETIME DEFAULT CURRENT_TIMESTAMP,
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            metadata TEXT DEFAULT '{}',
            UNIQUE(category, subcategory, product_name)
        );
        """
        
        # Categories table
        categories_table = """
        CREATE TABLE IF NOT EXISTS categories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT UNIQUE NOT NULL,
            description TEXT DEFAULT '',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # Subcategories table
        subcategories_table = """
        CREATE TABLE IF NOT EXISTS subcategories (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            category_id INTEGER NOT NULL,
            name TEXT NOT NULL,
            description TEXT DEFAULT '',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            FOREIGN KEY (category_id) REFERENCES categories (id) ON DELETE CASCADE,
            UNIQUE(category_id, name)
        );
        """
        
        # Canvas state table for saving workspace layouts
        canvas_state_table = """
        CREATE TABLE IF NOT EXISTS canvas_state (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            name TEXT NOT NULL,
            canvas_data TEXT NOT NULL,
            connections_data TEXT DEFAULT '[]',
            metadata TEXT DEFAULT '{}',
            created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
            last_modified DATETIME DEFAULT CURRENT_TIMESTAMP
        );
        """
        
        # Execute table creation
        self.conn.execute(products_table)
        self.conn.execute(categories_table)
        self.conn.execute(subcategories_table)
        self.conn.execute(canvas_state_table)
        self.conn.commit()
        
        logging.info("Database tables created successfully")

    def create_indexes(self):
        """Create indexes for better performance with large datasets"""
        indexes = [
            "CREATE INDEX IF NOT EXISTS idx_products_category ON products(category);",
            "CREATE INDEX IF NOT EXISTS idx_products_subcategory ON products(subcategory);",
            "CREATE INDEX IF NOT EXISTS idx_products_name ON products(product_name);",
            "CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);",
            "CREATE INDEX IF NOT EXISTS idx_products_last_updated ON products(last_updated);",
            "CREATE INDEX IF NOT EXISTS idx_categories_name ON categories(name);",
            "CREATE INDEX IF NOT EXISTS idx_subcategories_category ON subcategories(category_id);",
            "CREATE INDEX IF NOT EXISTS idx_canvas_name ON canvas_state(name);",
            "CREATE INDEX IF NOT EXISTS idx_canvas_modified ON canvas_state(last_modified);"
        ]
        
        for index_sql in indexes:
            self.conn.execute(index_sql)
        
        self.conn.commit()
        logging.info("Database indexes created successfully")

    def save_image(self, image_data, product_name):
        """
        Save image to file system and return path
        
        Args:
            image_data (str): Base64 encoded image data or binary data
            product_name (str): Product name for filename generation
            
        Returns:
            str: Relative path to saved image
        """
        try:
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            sanitized_name = "".join(c for c in product_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            sanitized_name = sanitized_name.replace(' ', '_')
            
            # Create hash for uniqueness
            hash_obj = hashlib.md5(f"{product_name}{timestamp}".encode())
            hash_suffix = hash_obj.hexdigest()[:8]
            
            filename = f"{sanitized_name}_{timestamp}_{hash_suffix}.png"
            file_path = self.images_path / filename
            
            # Handle base64 data
            if isinstance(image_data, str):
                if image_data.startswith('data:image'):
                    # Extract base64 part
                    image_data = image_data.split(',')[1]
                
                # Decode base64
                image_binary = base64.b64decode(image_data)
            else:
                image_binary = image_data
            
            # Save to file
            with open(file_path, 'wb') as f:
                f.write(image_binary)
            
            # Return relative path
            relative_path = f"Images/{filename}"
            logging.info(f"Image saved: {relative_path}")
            return relative_path
            
        except Exception as e:
            logging.error(f"Error saving image for {product_name}: {e}")
            return None

    def insert_or_replace_product(self, product_data):
        """
        Insert or replace product (handles duplicates based on category, subcategory, product_name)
        
        Args:
            product_data (dict): Product information
            
        Returns:
            dict: Result with success status and product ID
        """
        try:
            # Extract product data
            category = product_data.get('category', '')
            subcategory = product_data.get('subcategory', '')
            product_name = product_data.get('product_name', '')
            image_data = product_data.get('image_data')
            material = product_data.get('material', '')
            diameter = product_data.get('diameter', '')
            price = float(product_data.get('price', 0))
            quantity = int(product_data.get('quantity', 0))
            metadata = product_data.get('metadata', {})
            
            # Validate required fields
            if not all([category, subcategory, product_name]):
                raise ValueError("Category, subcategory, and product_name are required")
            
            # Save image if provided
            image_path = None
            if image_data:
                image_path = self.save_image(image_data, product_name)
            
            # Insert or replace product
            sql = """
            INSERT OR REPLACE INTO products 
            (category, subcategory, product_name, image_path, material, diameter, price, quantity, metadata, last_updated)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
            """
            
            cursor = self.conn.execute(sql, (
                category, subcategory, product_name, image_path, 
                material, diameter, price, quantity, json.dumps(metadata)
            ))
            
            self.conn.commit()
            product_id = cursor.lastrowid
            
            logging.info(f"Product saved: {product_name} (ID: {product_id})")
            
            return {
                'success': True,
                'product_id': product_id,
                'message': 'Product saved successfully',
                'image_path': image_path
            }
            
        except Exception as e:
            logging.error(f"Error saving product {product_data.get('product_name', 'Unknown')}: {e}")
            return {
                'success': False,
                'message': str(e)
            }

    def get_products(self, filters=None):
        """
        Get products with optional filtering
        
        Args:
            filters (dict): Optional filters (category, subcategory, search, limit)
            
        Returns:
            list: List of product dictionaries
        """
        try:
            filters = filters or {}
            
            # Build query
            sql = "SELECT * FROM products"
            conditions = []
            params = []
            
            # Add filters
            if filters.get('category'):
                conditions.append("category = ?")
                params.append(filters['category'])
            
            if filters.get('subcategory'):
                conditions.append("subcategory = ?")
                params.append(filters['subcategory'])
            
            if filters.get('search'):
                conditions.append("(product_name LIKE ? OR material LIKE ?)")
                search_term = f"%{filters['search']}%"
                params.extend([search_term, search_term])
            
            if conditions:
                sql += " WHERE " + " AND ".join(conditions)
            
            sql += " ORDER BY category, subcategory, product_name"
            
            # Add limit
            limit = filters.get('limit', 1000)
            sql += f" LIMIT {limit}"
            
            # Execute query
            cursor = self.conn.execute(sql, params)
            rows = cursor.fetchall()
            
            # Convert to dictionaries and load images
            products = []
            for row in rows:
                product = dict(row)
                
                # Parse metadata
                try:
                    product['metadata'] = json.loads(product['metadata'] or '{}')
                except:
                    product['metadata'] = {}
                
                # Load image if path exists
                if product['image_path']:
                    image_full_path = self.base_path / product['image_path']
                    if image_full_path.exists():
                        try:
                            with open(image_full_path, 'rb') as f:
                                image_data = base64.b64encode(f.read()).decode()
                                product['image'] = f"data:image/png;base64,{image_data}"
                        except Exception as e:
                            logging.warning(f"Could not load image {product['image_path']}: {e}")
                            product['image'] = None
                    else:
                        product['image'] = None
                
                products.append(product)
            
            logging.info(f"Retrieved {len(products)} products")
            return products
            
        except Exception as e:
            logging.error(f"Error getting products: {e}")
            return []

    def search_products(self, search_term, options=None):
        """
        Advanced product search with multiple criteria
        
        Args:
            search_term (str): Search term
            options (dict): Search options (categories, price_range, sort_by, etc.)
            
        Returns:
            list: Matching products
        """
        try:
            options = options or {}
            
            # Build advanced search query
            sql = """
            SELECT * FROM products 
            WHERE (product_name LIKE ? OR material LIKE ? OR diameter LIKE ? OR category LIKE ?)
            """
            search_pattern = f"%{search_term}%"
            params = [search_pattern, search_pattern, search_pattern, search_pattern]
            
            # Add category filter
            if options.get('categories'):
                placeholders = ','.join('?' * len(options['categories']))
                sql += f" AND category IN ({placeholders})"
                params.extend(options['categories'])
            
            # Add price range filter
            if options.get('price_range'):
                price_range = options['price_range']
                if price_range.get('min') is not None:
                    sql += " AND price >= ?"
                    params.append(price_range['min'])
                if price_range.get('max') is not None:
                    sql += " AND price <= ?"
                    params.append(price_range['max'])
            
            # Add sorting
            sort_by = options.get('sort_by', 'product_name')
            sort_order = options.get('sort_order', 'ASC')
            sql += f" ORDER BY {sort_by} {sort_order}"
            
            # Add limit
            limit = options.get('limit', 100)
            sql += f" LIMIT {limit}"
            
            # Execute search
            cursor = self.conn.execute(sql, params)
            rows = cursor.fetchall()
            
            # Process results
            products = []
            for row in rows:
                product = dict(row)
                try:
                    product['metadata'] = json.loads(product['metadata'] or '{}')
                except:
                    product['metadata'] = {}
                products.append(product)
            
            logging.info(f"Search '{search_term}' returned {len(products)} results")
            return products
            
        except Exception as e:
            logging.error(f"Error searching products: {e}")
            return []

    def get_database_stats(self):
        """Get comprehensive database statistics"""
        try:
            stats = {}
            
            # Total products
            cursor = self.conn.execute("SELECT COUNT(*) FROM products")
            stats['total_products'] = cursor.fetchone()[0]
            
            # Products by category
            cursor = self.conn.execute("""
                SELECT category, COUNT(*) as count 
                FROM products 
                GROUP BY category 
                ORDER BY count DESC
            """)
            stats['products_by_category'] = [
                {'category': row[0], 'count': row[1]} 
                for row in cursor.fetchall()
            ]
            
            # Database file size
            if self.db_path.exists():
                stats['database_size'] = self.db_path.stat().st_size
                stats['formatted_size'] = self.format_bytes(stats['database_size'])
            
            # Images directory size
            images_size = sum(f.stat().st_size for f in self.images_path.rglob('*') if f.is_file())
            stats['images_size'] = images_size
            stats['formatted_images_size'] = self.format_bytes(images_size)
            
            # Recent products
            cursor = self.conn.execute("""
                SELECT product_name, category, last_updated 
                FROM products 
                ORDER BY last_updated DESC 
                LIMIT 10
            """)
            stats['recent_products'] = [
                {'name': row[0], 'category': row[1], 'last_updated': row[2]}
                for row in cursor.fetchall()
            ]
            
            return stats
            
        except Exception as e:
            logging.error(f"Error getting database stats: {e}")
            return {}

    def format_bytes(self, bytes_value):
        """Format bytes to human readable format"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if bytes_value < 1024.0:
                return f"{bytes_value:.2f} {unit}"
            bytes_value /= 1024.0
        return f"{bytes_value:.2f} TB"

    def export_to_json(self, output_file=None):
        """Export entire database to JSON file"""
        try:
            if not output_file:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                output_file = self.base_path / f"plombdesign_export_{timestamp}.json"
            
            # Get all products
            products = self.get_products()
            stats = self.get_database_stats()
            
            export_data = {
                'export_info': {
                    'timestamp': datetime.now().isoformat(),
                    'version': '1.0.0',
                    'application': 'PlombDesign SQLite',
                    'total_products': len(products)
                },
                'products': products,
                'statistics': stats
            }
            
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(export_data, f, indent=2, ensure_ascii=False)
            
            logging.info(f"Database exported to: {output_file}")
            return str(output_file)
            
        except Exception as e:
            logging.error(f"Error exporting database: {e}")
            return None

    def close(self):
        """Close database connection"""
        if self.conn:
            self.conn.close()
            logging.info("Database connection closed")


# Sample usage and testing
if __name__ == "__main__":
    # Initialize the data storage system
    storage = PlombDesignDataStorage()
    
    # Sample image data (1x1 pixel PNG in base64)
    sample_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChwGA60e6kgAAAABJRU5ErkJggg=="
    
    # Sample products for testing
    sample_products = [
        {
            'category': 'Pipes & Fittings',
            'subcategory': 'Copper Pipes',
            'product_name': 'Copper Pipe 1/2"',
            'image_data': sample_image_data,
            'material': 'Copper',
            'diameter': '1/2"',
            'price': 12.99,
            'quantity': 50,
            'metadata': {'supplier': 'ABC Supply', 'weight': '1.2kg'}
        },
        {
            'category': 'Valves & Controls',
            'subcategory': 'Ball Valves',
            'product_name': 'Ball Valve 3/4"',
            'image_data': sample_image_data,
            'material': 'Brass',
            'diameter': '3/4"',
            'price': 25.50,
            'quantity': 30,
            'metadata': {'pressure_rating': '200 PSI'}
        }
    ]
    
    print("=== PlombDesign Data Storage System Demo ===")
    
    # Insert sample products
    print("\n1. Inserting sample products...")
    for product in sample_products:
        result = storage.insert_or_replace_product(product)
        print(f"   {product['product_name']}: {'✓' if result['success'] else '✗'}")
    
    # Get all products
    print("\n2. Retrieving all products...")
    all_products = storage.get_products()
    print(f"   Found {len(all_products)} products")
    
    # Search products
    print("\n3. Searching for 'copper'...")
    search_results = storage.search_products('copper')
    print(f"   Found {len(search_results)} matching products")
    
    # Get database statistics
    print("\n4. Database statistics:")
    stats = storage.get_database_stats()
    print(f"   Total products: {stats.get('total_products', 0)}")
    print(f"   Database size: {stats.get('formatted_size', 'Unknown')}")
    print(f"   Images size: {stats.get('formatted_images_size', 'Unknown')}")
    
    # Export database
    print("\n5. Exporting database...")
    export_file = storage.export_to_json()
    if export_file:
        print(f"   Exported to: {export_file}")
    
    # Close connection
    storage.close()
    print("\n✓ Demo completed successfully!")
