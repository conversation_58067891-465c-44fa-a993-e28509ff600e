import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  X,
  <PERSON>ting<PERSON>,
  Eye,
  EyeOff,
  Save,
  Upload,
  Download,
  Trash2,
  RotateCcw,
  Grid,
  Clock,
  Shield,
  Palette,
  Database,
  Minus,
  Plus,
  ChevronDown,
  ChevronRight,
  Package,
  Tag,
  Search,
  History,
  Edit,
  FileSpreadsheet,
  AlertCircle,
  AlertTriangle,
  RefreshCw,
  CheckCircle,
  HardDrive,
  BarChart3
} from 'lucide-react';
import * as XLSX from 'xlsx';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';
import { useData } from '../contexts/DataContext';
import { productCategories, defaultProducts } from '../data/products';
import { searchProducts } from '../utils/searchUtils';
import { clearProductImageCache } from '../utils/productImageUtils';
import ProductTemplateBuilder from './ProductTemplateBuilder';
import { unifiedDataOps } from '../utils/unifiedDataOperations';
import { enhancedDataStorage } from '../utils/enhancedDataStorage';
import { sqliteManager } from '../utils/sqliteManager';

const SettingsModal = ({
  isOpen,
  onClose,
  canvasProducts,
  connections,
  onCategoryChanges,
  onLoadProject
}) => {
  const { t, language, setLanguage } = useLanguage();
  const {
    settings,
    customProducts,
    customCategories,
    getAllCategories,
    ensureCategoryExists,
    updateSetting,
    updateCategoryVisibility,
    resetCategoryVisibility,
    removeCategory,
    restoreCategory,
    permanentlyDeleteCategory,
    removeSubcategory,
    restoreSubcategory,
    permanentlyDeleteSubcategory,
    permanentlyDeleteProduct,
    getCategoryProductCount,
    getSubcategoryProductCount,
    getAvailableCategories,
    getAvailableSubcategories,
    reassignProducts,
    addCustomProduct,
    updateCustomProduct,
    clearAllData,
    clearCategoriesData,
    resetSettings,
    getDataSummary,
    restoreDefaultData,
    importSettings,
    updateSettings
  } = useSettings();

  // Data management context
  const {
    storageInfo,
    lastSaveTime,
    autoSaveEnabled,
    hasLoadedData,
    saveApplicationData,
    exportData,
    importData,
    getBackups,
    restoreFromBackup,
    deleteBackup,
    deleteMultipleBackups,
    clearAllData: clearDataStorage,
    saveSettings: saveDataSettings,
    // Enhanced storage
    sqliteEnabled,
    migrationStatus,
    databaseStats,
    saveProductToDatabase,
    getProductsFromDatabase,
    searchProductsInDatabase,
    refreshDatabaseStats,
    initializeEnhancedStorage
  } = useData();

  // State for managing tabs and UI
  const [activeTab, setActiveTab] = useState('categories');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState({});
  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationAction, setConfirmationAction] = useState(null);
  const [editingItem, setEditingItem] = useState(null);

  // State for Excel functionality
  const [isExporting, setIsExporting] = useState(false);
  const [importStatus, setImportStatus] = useState(null);

  // State for enhanced clear all data dialog
  const [showClearAllDialog, setShowClearAllDialog] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  // State for product editing
  const [editingProduct, setEditingProduct] = useState(null);
  const [showEditProductModal, setShowEditProductModal] = useState(false);
  const [editProductForm, setEditProductForm] = useState({
    name: '',
    category: '',
    subcategory: '',
    price: '',
    size: '',
    material: '',
    image: ''
  });
  const [editProductErrors, setEditProductErrors] = useState({});
  const [isUpdatingProduct, setIsUpdatingProduct] = useState(false);

  // State for image editing
  const [imagePreview, setImagePreview] = useState('');
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [imageUploadError, setImageUploadError] = useState('');

  // State for category editing
  const [editingCategory, setEditingCategory] = useState(null);
  const [editCategoryForm, setEditCategoryForm] = useState({
    name: '',
    description: ''
  });
  const [editCategoryErrors, setEditCategoryErrors] = useState({});

  // State for Standalone Product Template Builder
  const [showStandaloneTemplateBuilder, setShowStandaloneTemplateBuilder] = useState(false);

  // State for deletion confirmation modals
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);
  const [deleteType, setDeleteType] = useState(''); // 'category' or 'subcategory'
  const [productHandlingOption, setProductHandlingOption] = useState('orphan');
  const [reassignTarget, setReassignTarget] = useState({ category: '', subcategory: '' });
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState('');

  // State for data storage management
  const [dataStorageTab, setDataStorageTab] = useState('backups');
  const [backups, setBackups] = useState([]);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const [dataOperationStatus, setDataOperationStatus] = useState(null);
  const [showDeleteBackupConfirm, setShowDeleteBackupConfirm] = useState(false);
  const [backupToDelete, setBackupToDelete] = useState(null);
  const [showDeleteAllBackupsConfirm, setShowDeleteAllBackupsConfirm] = useState(false);
  const [isDeletingAllBackups, setIsDeletingAllBackups] = useState(false);

  // State for unified database operations
  const [operationProgress, setOperationProgress] = useState(null);
  const [currentOperation, setCurrentOperation] = useState(null);
  const [enhancedStats, setEnhancedStats] = useState(null);

  // Combine default and custom products and ensure backward compatibility
  const allProducts = useMemo(() => {
    const ensureQuantity = (product) => ({
      ...product,
      quantity: typeof product.quantity === 'number' ? product.quantity : 1
    });

    return [
      ...defaultProducts.map(ensureQuantity),
      ...customProducts.map(ensureQuantity)
    ];
  }, [customProducts]);

  // Initialize expanded categories
  useEffect(() => {
    if (productCategories.length > 0 && Object.keys(expandedCategories).length === 0) {
      setExpandedCategories(
        productCategories.reduce((acc, category) => ({
          ...acc,
          [category.name]: false
        }), {})
      );
    }
  }, []);

  // Effect to handle tab initialization
  useEffect(() => {
    if (isOpen) {
      // Reset states when modal opens
      setSearchTerm('');
      setConfirmationDialogOpen(false);
      setConfirmationAction(null);
      setEditingItem(null);
    }
  }, [isOpen]);

  // Filter products by search term and category/product deletion status
  const filteredProducts = useMemo(() => {
    let filtered = allProducts;

    // Filter by search term using enhanced search utility
    if (searchTerm && searchTerm.trim()) {
      filtered = searchProducts(filtered, searchTerm);
    }

    // Filter out products from deleted categories
    filtered = filtered.filter(product =>
      !(settings.deletedCategories || []).includes(product.category)
    );

    // Filter out products from deleted subcategories
    filtered = filtered.filter(product => {
      const isSubcategoryDeleted = (settings.deletedSubcategories || []).some(
        item => item.category === product.category && item.subcategory === product.subcategory
      );
      return !isSubcategoryDeleted;
    });

    // Filter out individually deleted products
    filtered = filtered.filter(product =>
      !(settings.deletedProducts || []).includes(product.id)
    );

    return filtered;
  }, [allProducts, searchTerm, settings.deletedCategories, settings.deletedSubcategories, settings.deletedProducts]);



  // Toggle category visibility
  const toggleCategoryVisibility = (categoryName) => {
    updateCategoryVisibility(categoryName, !settings.visibleCategories[categoryName]);
    if (onCategoryChanges) {
      onCategoryChanges();
    }
  };

  // Toggle category expansion in the UI
  const toggleCategoryExpanded = (categoryName) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }));
  };

  // Enhanced clear all data with detailed confirmation
  const confirmClearAllData = () => {
    setShowClearAllDialog(true);
  };

  // Product editing functions
  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setEditProductForm({
      name: product.name || '',
      category: product.category || '',
      subcategory: product.subcategory || '',
      price: product.price || '',
      size: product.size || product.diameter || '',
      material: product.material || '',
      image: product.image || ''
    });
    setEditProductErrors({});
    setImagePreview(product.image || '');
    setImageUploadError('');
    setShowEditProductModal(true);
  };

  const handleEditProductFormChange = (field, value) => {
    setEditProductForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (editProductErrors[field]) {
      setEditProductErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateEditProductForm = () => {
    const errors = {};

    if (!editProductForm.name.trim()) {
      errors.name = t('Product name is required') || 'Product name is required';
    }

    if (!editProductForm.category.trim()) {
      errors.category = t('Category is required') || 'Category is required';
    }

    if (!editProductForm.subcategory.trim()) {
      errors.subcategory = t('Subcategory is required') || 'Subcategory is required';
    }

    if (editProductForm.price && isNaN(parseFloat(editProductForm.price))) {
      errors.price = t('Price must be a valid number') || 'Price must be a valid number';
    }

    return errors;
  };

  const handleSaveEditedProduct = async () => {
    const errors = validateEditProductForm();

    if (Object.keys(errors).length > 0) {
      setEditProductErrors(errors);
      return;
    }

    setIsUpdatingProduct(true);

    try {
      const updatedProduct = {
        ...editingProduct,
        name: editProductForm.name.trim(),
        category: editProductForm.category.trim(),
        subcategory: editProductForm.subcategory.trim(),
        price: editProductForm.price ? parseFloat(editProductForm.price) : editingProduct.price,
        size: editProductForm.size.trim() || undefined,
        diameter: editProductForm.size.trim() || undefined,
        material: editProductForm.material.trim() || undefined,
        image: editProductForm.image.trim() || undefined
      };

      // Clear PNG cache for this product if image changed
      if (editingProduct.image !== updatedProduct.image) {
        clearProductImageCache();
      }

      // Check if this is a custom product or default product
      const isCustomProduct = customProducts.some(p => p.id === editingProduct.id);

      if (isCustomProduct) {
        // Update custom product
        updateCustomProduct(editingProduct.id, updatedProduct);
      } else {
        // For default products, we need to create a custom version
        // and mark the original as deleted/removed
        const newCustomProduct = {
          ...updatedProduct,
          id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          isCustom: true,
          originalId: editingProduct.id
        };

        // Add the custom product
        addCustomProduct(newCustomProduct);

        // Mark the original product as removed/deleted
        updateSetting('removedProducts', [...(settings.removedProducts || []), editingProduct.id]);
        updateSetting('deletedProducts', [...(settings.deletedProducts || []), editingProduct.id]);
      }

      // Trigger category changes callback
      if (onCategoryChanges) {
        onCategoryChanges();
      }

      // Close the edit modal
      setShowEditProductModal(false);
      setEditingProduct(null);

    } catch (error) {
      console.error('Error updating product:', error);
      setEditProductErrors({
        general: t('Failed to update product') || 'Failed to update product'
      });
    } finally {
      setIsUpdatingProduct(false);
    }
  };

  const handleCancelEditProduct = () => {
    setShowEditProductModal(false);
    setEditingProduct(null);
    setEditProductForm({
      name: '',
      category: '',
      subcategory: '',
      price: '',
      size: '',
      material: '',
      image: ''
    });
    setEditProductErrors({});
    setImagePreview('');
    setImageUploadError('');
  };

  // Image handling functions
  const validateImageFile = (file) => {
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return t('Unsupported image format. Please use PNG, JPG, JPEG, or WebP.') || 'Unsupported image format. Please use PNG, JPG, JPEG, or WebP.';
    }

    if (file.size > maxSize) {
      return t('Image size must be less than 2MB') || 'Image size must be less than 2MB';
    }

    return null;
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const validationError = validateImageFile(file);
    if (validationError) {
      setImageUploadError(validationError);
      event.target.value = '';
      return;
    }

    setImageUploadError('');
    setIsProcessingImage(true);

    const reader = new FileReader();
    reader.onload = (e) => {
      const base64Data = e.target.result;
      setImagePreview(base64Data);
      setEditProductForm(prev => ({ ...prev, image: base64Data }));
      setIsProcessingImage(false);
    };

    reader.onerror = () => {
      setIsProcessingImage(false);
      setImageUploadError(t('Error processing image') || 'Error processing image');
    };

    reader.readAsDataURL(file);
    event.target.value = '';
  };

  const handleRemoveImage = () => {
    setImagePreview('');
    setEditProductForm(prev => ({ ...prev, image: '' }));
    setImageUploadError('');
  };

  const handleImageUrlChange = (url) => {
    setEditProductForm(prev => ({ ...prev, image: url }));
    setImagePreview(url);
    setImageUploadError('');
  };

  // Category editing functions
  const handleEditCategory = (category) => {
    setEditingCategory(category.name);
    setEditCategoryForm({
      name: category.name,
      description: category.description || ''
    });
    setEditCategoryErrors({});
  };

  const handleEditCategoryFormChange = (field, value) => {
    setEditCategoryForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (editCategoryErrors[field]) {
      setEditCategoryErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateEditCategoryForm = () => {
    const errors = {};
    const allCategories = getAllCategories();

    if (!editCategoryForm.name.trim()) {
      errors.name = t('Category name is required') || 'Category name is required';
    } else {
      // Check for duplicate names (excluding the current category being edited)
      const isDuplicate = allCategories.some(cat =>
        cat.name.toLowerCase() === editCategoryForm.name.trim().toLowerCase() &&
        cat.name !== editingCategory
      );
      if (isDuplicate) {
        errors.name = t('Category name already exists') || 'Category name already exists';
      }
    }

    return errors;
  };

  const handleSaveEditedCategory = async () => {
    const errors = validateEditCategoryForm();

    if (Object.keys(errors).length > 0) {
      setEditCategoryErrors(errors);
      return;
    }

    try {
      const newName = editCategoryForm.name.trim();
      const oldName = editingCategory;

      // If name changed, we need to update all products in this category
      if (newName !== oldName) {
        // For now, we'll just update the custom categories
        // In a full implementation, you'd want to update all products too
        const customCategoryIndex = customCategories.findIndex(cat => cat.name === oldName);
        if (customCategoryIndex !== -1) {
          const updatedCategories = [...customCategories];
          updatedCategories[customCategoryIndex] = {
            ...updatedCategories[customCategoryIndex],
            name: newName,
            description: editCategoryForm.description
          };
          // Note: This would need to be implemented in the SettingsContext
          // For now, we'll just show a message
          console.log('Category update would happen here');
        }
      }

      // Reset editing state
      setEditingCategory(null);
      setEditCategoryForm({ name: '', description: '' });
      setEditCategoryErrors({});

      // Trigger category changes callback
      if (onCategoryChanges) {
        onCategoryChanges();
      }

    } catch (error) {
      console.error('Error updating category:', error);
      setEditCategoryErrors({
        general: t('Failed to update category') || 'Failed to update category'
      });
    }
  };

  const handleCancelEditCategory = () => {
    setEditingCategory(null);
    setEditCategoryForm({ name: '', description: '' });
    setEditCategoryErrors({});
  };

  // Handle the actual clear all data operation
  const handleClearAllData = async () => {
    setIsClearing(true);

    try {
      console.log('Starting comprehensive clear all data operation...');

      // Clear enhanced data storage (SQLite + localStorage + images + backups)
      const enhancedResult = await enhancedDataStorage.clearAllData();
      console.log('Enhanced storage clear result:', enhancedResult);

      // Clear settings data (custom products, categories, etc.)
      const settingsResult = clearAllData();
      console.log('Settings clear result:', settingsResult);

      // Clear data context storage
      const storageResult = clearDataStorage();
      console.log('Data context clear result:', storageResult);

      // Determine overall success
      const overallSuccess = enhancedResult.success && settingsResult.success && storageResult.success;

      if (overallSuccess) {
        // Trigger category changes callback to refresh UI
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        // Refresh database stats
        if (refreshDatabaseStats) {
          try {
            await refreshDatabaseStats();
          } catch (statsError) {
            console.warn('Failed to refresh database stats after clear:', statsError);
          }
        }

        // Show comprehensive success message
        const successMessage = `All application data cleared successfully. ${enhancedResult.message}`;
        setDataOperationStatus({
          type: 'success',
          message: successMessage
        });

        console.log('Clear all data operation completed successfully');

        // Close dialog after success
        setShowClearAllDialog(false);

        // Reload enhanced stats to reflect changes
        setTimeout(async () => {
          try {
            await loadEnhancedStats();
          } catch (error) {
            console.warn('Failed to reload stats after clear:', error);
          }
        }, 1000);

      } else {
        // Collect error messages
        const errorMessages = [];
        if (!enhancedResult.success) errorMessages.push(`Enhanced storage: ${enhancedResult.message}`);
        if (!settingsResult.success) errorMessages.push(`Settings: ${settingsResult.message}`);
        if (!storageResult.success) errorMessages.push(`Data context: ${storageResult.message}`);

        const errorMessage = 'Clear data partially failed: ' + errorMessages.join('; ');
        console.error('Clear all data operation failed:', errorMessage);

        // Show error message
        setDataOperationStatus({
          type: 'error',
          message: errorMessage
        });

        // Close dialog
        setShowClearAllDialog(false);
      }
    } catch (error) {
      console.error('Error during clear all data operation:', error);

      // Show error message
      setDataOperationStatus({
        type: 'error',
        message: 'Clear data failed: ' + error.message
      });

      // Close dialog
      setShowClearAllDialog(false);
    } finally {
      setIsClearing(false);
    }
  };







  // Functions for deletion confirmation
  const handleDeleteCategory = (categoryName) => {
    const productCount = getCategoryProductCount(categoryName);
    setDeleteTarget({ name: categoryName, productCount });
    setDeleteType('category');
    setProductHandlingOption('orphan');
    setReassignTarget({ category: '', subcategory: '' });
    setDeleteError('');
    setShowDeleteConfirm(true);
  };

  const handleDeleteSubcategory = (categoryName, subcategoryName) => {
    const productCount = getSubcategoryProductCount(categoryName, subcategoryName);
    setDeleteTarget({
      name: subcategoryName,
      category: categoryName,
      productCount
    });
    setDeleteType('subcategory');
    setProductHandlingOption('orphan');
    setReassignTarget({ category: '', subcategory: '' });
    setDeleteError('');
    setShowDeleteConfirm(true);
  };

  const handleConfirmDeletion = async () => {
    setIsDeleting(true);
    setDeleteError('');

    try {
      let result;

      if (deleteType === 'category') {
        if (productHandlingOption === 'reassign' && reassignTarget.category) {
          // First reassign products, then delete category
          await reassignProducts(
            deleteTarget.name,
            null,
            reassignTarget.category,
            reassignTarget.subcategory || 'Other'
          );
        }

        result = permanentlyDeleteCategory(deleteTarget.name, productHandlingOption);
      } else if (deleteType === 'subcategory') {
        if (productHandlingOption === 'reassign' && reassignTarget.subcategory) {
          // First reassign products, then delete subcategory
          await reassignProducts(
            deleteTarget.category,
            deleteTarget.name,
            deleteTarget.category,
            reassignTarget.subcategory
          );
        }

        result = permanentlyDeleteSubcategory(
          deleteTarget.category,
          deleteTarget.name,
          productHandlingOption
        );
      }

      if (result && result.success) {
        setShowDeleteConfirm(false);
        // Force re-render by updating a state variable
        setExpandedCategories(prev => ({ ...prev }));
        // Force refresh of search term to trigger filteredProducts recalculation
        setSearchTerm(prev => prev);
        // Trigger category changes callback if provided
        if (onCategoryChanges) {
          onCategoryChanges();
        }
        // Show success message
        console.log(`Successfully deleted ${deleteType}: ${deleteTarget.name}`);
      } else {
        setDeleteError(result?.error || 'Deletion failed');
      }
    } catch (error) {
      console.error('Error during deletion:', error);
      setDeleteError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDeletion = () => {
    setShowDeleteConfirm(false);
    setDeleteTarget(null);
    setDeleteType('');
    setProductHandlingOption('orphan');
    setReassignTarget({ category: '', subcategory: '' });
    setDeleteError('');
    setIsDeleting(false);
  };






  // Function to handle Excel import
  const handleExcelImport = (event) => {
    const file = event.target.files[0];
    if (!file) {
      console.log('No file selected for import');
      return;
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
      console.error('Invalid file type:', file.type);
      setImportStatus({
        type: 'error',
        message: `${t('excelImportError') || 'Error importing Excel file'}: Please select a .xlsx file`
      });
      setTimeout(() => setImportStatus(null), 5000);
      return;
    }

    console.log('=== EXCEL IMPORT STARTED ===');
    console.log('File name:', file.name);
    console.log('File size:', file.size, 'bytes');
    console.log('File type:', file.type);

    setImportStatus({ type: 'info', message: t('processingImages') || 'Processing Excel file...' });

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        console.log('=== FILE READ SUCCESSFUL ===');
        console.log('ArrayBuffer size:', e.target.result.byteLength);

        const data = new Uint8Array(e.target.result);
        console.log('Converting to Uint8Array, length:', data.length);

        const workbook = XLSX.read(data, { type: 'array' });
        console.log('=== EXCEL PARSING SUCCESSFUL ===');
        console.log('Available sheets:', workbook.SheetNames);

        // Get the Products worksheet with more flexible matching
        let productsSheetName = workbook.SheetNames.find(name => {
          const lowerName = name.toLowerCase();
          return lowerName.includes('product') ||
                 lowerName.includes('produit') ||
                 lowerName === 'products' ||
                 lowerName === 'produits';
        });

        // If no products sheet found, use the first sheet
        if (!productsSheetName) {
          productsSheetName = workbook.SheetNames[0];
          console.warn('No products sheet found, using first sheet:', productsSheetName);
        } else {
          console.log('Found products sheet:', productsSheetName);
        }

        if (!productsSheetName || workbook.SheetNames.length === 0) {
          console.error('No worksheets found in file');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: No worksheets found in file`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        console.log('=== PROCESSING WORKSHEET ===');
        console.log('Using sheet:', productsSheetName);

        const worksheet = workbook.Sheets[productsSheetName];
        if (!worksheet) {
          console.error('Worksheet not found:', productsSheetName);
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: Worksheet "${productsSheetName}" not found`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        console.log('=== DATA PARSING SUCCESSFUL ===');
        console.log('Parsed data rows:', jsonData.length);
        console.log('Sample row (first):', jsonData[0]);
        console.log('Available columns:', jsonData.length > 0 ? Object.keys(jsonData[0]) : 'No data');

        if (jsonData.length === 0) {
          console.warn('No data rows found in worksheet');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: The sheet "${productsSheetName}" contains no data rows`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        // Validate that required functions are available
        if (typeof ensureCategoryExists !== 'function') {
          console.error('ensureCategoryExists function not available');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: Category management function not available`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        if (typeof addCustomProduct !== 'function') {
          console.error('addCustomProduct function not available');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: Product creation function not available`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        console.log('=== STARTING PRODUCT PROCESSING ===');
        let importedCount = 0;
        let errorCount = 0;
        const errors = [];
        let productsWithImages = 0;
        let productsWithoutImages = 0;

        jsonData.forEach((row, index) => {
          try {
            console.log(`=== PROCESSING ROW ${index + 2} ===`);
            console.log('Raw row data:', row);

            // Map column names with comprehensive support for different naming conventions
            const name = row['Name'] || row['Nom'] || row['name'] || row['nom'] ||
                        row['Product Name'] || row['Nom du Produit'] || '';
            const category = row['Category'] || row['Catégorie'] || row['category'] || row['catégorie'] || '';
            const subcategory = row['Subcategory'] || row['Sous-catégorie'] || row['subcategory'] ||
                              row['sous-catégorie'] || row['Sub-category'] || row['Sous catégorie'] || '';
            const priceRaw = row['Price'] || row['Prix'] || row['price'] || row['prix'] || 0;
            const size = row['Size'] || row['Taille'] || row['size'] || row['taille'] ||
                        row['Diameter'] || row['Diamètre'] || row['diameter'] || row['diamètre'] || '';
            const material = row['Material'] || row['Matériau'] || row['material'] || row['matériau'] ||
                           row['Materials'] || row['Matériaux'] || '';
            const image = row['Image (URL or base64)'] || row['Image (URL ou base64)'] ||
                         row['Image'] || row['image'] || row['Image URL'] || row['URL Image'] || '';

            console.log('Mapped values:', {
              name: name,
              category: category,
              subcategory: subcategory,
              priceRaw: priceRaw,
              size: size,
              material: material,
              imageLength: image ? image.length : 0
            });

            // Skip completely empty rows
            const hasAnyData = name || category || subcategory || priceRaw || size || material || image;
            if (!hasAnyData) {
              console.log(`Row ${index + 2}: Skipping completely empty row`);
              return;
            }

            // Validate required fields
            const missingFields = [];
            if (!name || (typeof name === 'string' && name.trim() === '')) {
              missingFields.push('name');
            }
            if (!category || (typeof category === 'string' && category.trim() === '')) {
              missingFields.push('category');
            }
            if (!subcategory || (typeof subcategory === 'string' && subcategory.trim() === '')) {
              missingFields.push('subcategory');
            }

            if (missingFields.length > 0) {
              const errorMsg = `Row ${index + 2}: Missing required fields: ${missingFields.join(', ')}`;
              console.warn(errorMsg);
              errors.push(errorMsg);
              errorCount++;
              return;
            }

            // Clean and validate data types
            const cleanName = name.toString().trim();
            const cleanCategory = category.toString().trim();
            const cleanSubcategory = subcategory.toString().trim();

            // Parse price with better error handling
            let cleanPrice = 0;
            if (priceRaw !== null && priceRaw !== undefined && priceRaw !== '') {
              const parsedPrice = parseFloat(priceRaw);
              if (!isNaN(parsedPrice) && parsedPrice >= 0) {
                cleanPrice = parsedPrice;
              } else {
                console.warn(`Row ${index + 2}: Invalid price value "${priceRaw}", using 0`);
              }
            }

            const cleanSize = size ? size.toString().trim() : '';
            const cleanMaterial = material ? material.toString().trim() : '';
            let cleanImage = image ? image.toString().trim() : '';

            console.log('Cleaned values:', {
              cleanName,
              cleanCategory,
              cleanSubcategory,
              cleanPrice,
              cleanSize,
              cleanMaterial,
              cleanImageLength: cleanImage.length
            });

            // Final validation of required fields after cleaning
            if (cleanName.length === 0 || cleanCategory.length === 0 || cleanSubcategory.length === 0) {
              const errorMsg = `Row ${index + 2}: Required fields cannot be empty after trimming`;
              console.warn(errorMsg);
              errors.push(errorMsg);
              errorCount++;
              return;
            }

            // Validate and process image data
            console.log(`Row ${index + 2}: Processing image data...`);
            if (cleanImage) {
              console.log(`Row ${index + 2}: Image data length: ${cleanImage.length}`);
              console.log(`Row ${index + 2}: Image data preview: ${cleanImage.substring(0, 100)}...`);

              // Check if it's a URL
              if (cleanImage.startsWith('http://') || cleanImage.startsWith('https://')) {
                try {
                  new URL(cleanImage);
                  console.log(`Row ${index + 2}: Valid image URL detected: ${cleanImage}`);
                } catch (urlError) {
                  console.warn(`Row ${index + 2}: Invalid image URL: ${cleanImage}`);
                  errors.push(`Row ${index + 2}: ${t('invalidImageUrl') || 'Invalid image URL'}: ${cleanImage.substring(0, 50)}...`);
                  cleanImage = ''; // Clear invalid URL but continue with import
                }
              }
              // Check if it's base64 data
              else if (cleanImage.startsWith('data:image/')) {
                const base64Pattern = /^data:image\/(jpeg|jpg|png|gif|svg\+xml);base64,([A-Za-z0-9+/=]+)$/;
                if (!base64Pattern.test(cleanImage)) {
                  console.warn(`Row ${index + 2}: Invalid base64 image data format`);
                  errors.push(`Row ${index + 2}: ${t('invalidImageData') || 'Invalid image data'} - must be valid base64 format`);
                  cleanImage = ''; // Clear invalid base64 but continue with import
                } else {
                  console.log(`Row ${index + 2}: Valid base64 image detected (${cleanImage.length} chars)`);
                }
              }
              // If it's not empty but doesn't match expected formats
              else if (cleanImage.length > 0) {
                console.warn(`Row ${index + 2}: Image data doesn't match expected format (URL or base64): ${cleanImage.substring(0, 50)}...`);
                errors.push(`Row ${index + 2}: ${t('invalidImageData') || 'Invalid image data'} - must be URL or base64 format`);
                cleanImage = ''; // Clear invalid data but continue with import
              }
            } else {
              console.log(`Row ${index + 2}: No image data provided`);
            }

            // Ensure category and subcategory exist
            console.log(`Row ${index + 2}: Creating category "${cleanCategory}" with subcategory "${cleanSubcategory}"`);
            try {
              ensureCategoryExists(cleanCategory, cleanSubcategory);
              console.log(`Row ${index + 2}: Category creation successful`);
            } catch (categoryError) {
              console.error(`Row ${index + 2}: Category creation failed:`, categoryError);
              const errorMsg = `Row ${index + 2}: Failed to create category "${cleanCategory}" > "${cleanSubcategory}": ${categoryError.message}`;
              errors.push(errorMsg);
              errorCount++;
              return;
            }

            // Create product object with unique ID
            const product = {
              id: `imported-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`,
              name: cleanName,
              category: cleanCategory,
              subcategory: cleanSubcategory,
              price: cleanPrice,
              diameter: cleanSize, // For backward compatibility
              size: cleanSize,
              material: cleanMaterial,
              image: cleanImage,
              isCustom: true,
              createdAt: new Date().toISOString()
            };

            console.log(`Row ${index + 2}: Created product object:`, product);

            // Track image statistics
            if (cleanImage) {
              productsWithImages++;
              console.log(`Row ${index + 2}: Product has image (${cleanImage.length} chars)`);
            } else {
              productsWithoutImages++;
              console.log(`Row ${index + 2}: Product has no image`);
            }

            // Add the product
            console.log(`Row ${index + 2}: Adding product to system...`);
            try {
              const addedProduct = addCustomProduct(product);
              importedCount++;
              console.log(`Row ${index + 2}: ✅ Successfully imported product: "${product.name}" (${product.category} > ${product.subcategory})${cleanImage ? ' with image' : ''}`);
              console.log(`Row ${index + 2}: Added product ID: ${addedProduct?.id || 'unknown'}`);
            } catch (productError) {
              console.error(`Row ${index + 2}: Product creation failed:`, productError);
              const errorMsg = `Row ${index + 2}: Failed to create product "${cleanName}": ${productError.message}`;
              errors.push(errorMsg);
              errorCount++;
            }

          } catch (error) {
            const errorMsg = `Row ${index + 2}: Unexpected error: ${error.message}`;
            console.error(`Row ${index + 2}: ❌ Unexpected error:`, error);
            errors.push(errorMsg);
            errorCount++;
          }
        });

        console.log('=== IMPORT COMPLETED ===');
        console.log(`Total processed: ${jsonData.length} rows`);
        console.log(`Successfully imported: ${importedCount} products`);
        console.log(`Errors encountered: ${errorCount}`);
        console.log(`Products with images: ${productsWithImages}`);
        console.log(`Products without images: ${productsWithoutImages}`);
        console.log('Error details:', errors);

        // Show comprehensive import results
        if (importedCount > 0) {
          const productsText = importedCount === 1 ? 'product' : 'products';
          let successMessage = `${t('excelImportSuccess') || 'Products imported successfully'}: ${importedCount} ${productsText}`;

          // Add detailed image statistics
          if (productsWithImages > 0 || productsWithoutImages > 0) {
            const imageStats = [];
            if (productsWithImages > 0) {
              imageStats.push(`${productsWithImages} ${t('importedWithImages') || 'with images'}`);
            }
            if (productsWithoutImages > 0) {
              imageStats.push(`${productsWithoutImages} ${t('importedWithoutImages') || 'without images'}`);
            }
            successMessage += ` (${imageStats.join(', ')})`;
          }

          // Add error summary if there are errors
          if (errorCount > 0) {
            successMessage += `. ${errorCount} row${errorCount === 1 ? '' : 's'} had errors and ${errorCount === 1 ? 'was' : 'were'} skipped.`;
          }

          setImportStatus({
            type: 'success',
            message: successMessage
          });

          // Log detailed success info
          console.log('✅ IMPORT SUCCESS:', successMessage);

        } else {
          // No products imported - show detailed error information
          let errorMessage;
          if (errors.length > 0) {
            errorMessage = `${t('excelImportError') || 'Error importing Excel file'}: ${errors[0]}`;
            if (errors.length > 1) {
              errorMessage += ` (and ${errors.length - 1} other error${errors.length > 2 ? 's' : ''})`;
            }
          } else {
            errorMessage = `${t('excelImportError') || 'Error importing Excel file'}: No valid products found in the file`;
          }

          setImportStatus({
            type: 'error',
            message: errorMessage
          });

          // Log detailed error info
          console.log('❌ IMPORT FAILED:', errorMessage);
          console.log('All errors:', errors);
        }

        // Show status for longer time if there are errors to review
        const statusTimeout = errorCount > 0 ? 8000 : 5000;
        setTimeout(() => setImportStatus(null), statusTimeout);

      } catch (error) {
        console.error('=== CRITICAL IMPORT ERROR ===');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);

        let errorMessage = `${t('excelImportError') || 'Error importing Excel file'}: `;

        // Provide specific error messages based on error type
        if (error.message.includes('Invalid file format')) {
          errorMessage += 'Invalid Excel file format. Please ensure the file is a valid .xlsx file.';
        } else if (error.message.includes('Cannot read property')) {
          errorMessage += 'File structure error. Please check that the Excel file has the correct format.';
        } else if (error.message.includes('XLSX')) {
          errorMessage += 'Excel parsing error. The file may be corrupted or in an unsupported format.';
        } else {
          errorMessage += error.message;
        }

        setImportStatus({
          type: 'error',
          message: errorMessage
        });
        setTimeout(() => setImportStatus(null), 8000);
      }
    };

    reader.onerror = (error) => {
      console.error('=== FILE READER ERROR ===');
      console.error('FileReader error:', error);
      console.error('File details:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      setImportStatus({
        type: 'error',
        message: `${t('excelImportError') || 'Error importing Excel file'}: Failed to read file. The file may be corrupted or too large.`
      });
      setTimeout(() => setImportStatus(null), 8000);
    };

    console.log('=== STARTING FILE READ ===');
    reader.readAsArrayBuffer(file);

    // Reset the input value so the same file can be imported again
    event.target.value = '';
  };







  // Render the modal content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'categories':
        return renderCategoriesTab();
      case 'products':
        return renderProductsTab();
      case 'settings':
        return renderSettingsTab();
      case 'database':
        return renderDatabaseTab();
      default:
        return renderCategoriesTab();
    }
  };

  // Render Database Tab (Unified Interface)
  const renderDatabaseTab = () => {
    return (
      <div className="p-4 flex flex-col h-full">
        <h3 className="font-bold mb-6 text-gray-900 text-xl">Database Management</h3>

        {/* Status Messages */}
        {dataOperationStatus && (
          <div className={`mb-4 p-3 rounded ${
            dataOperationStatus.type === 'success'
              ? 'bg-green-50 border border-green-200 text-green-800'
              : dataOperationStatus.type === 'info'
              ? 'bg-blue-50 border border-blue-200 text-blue-800'
              : dataOperationStatus.type === 'warning'
              ? 'bg-yellow-50 border border-yellow-200 text-yellow-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            {dataOperationStatus.message}
          </div>
        )}

        {/* Operation Progress */}
        {operationProgress && (
          <div className="mb-4 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <div className="flex items-center justify-between mb-2">
              <span className="text-sm font-medium">
                {currentOperation === 'export' ? 'Exporting Data' :
                 currentOperation === 'import' ? 'Importing Data' :
                 currentOperation === 'initialize' ? 'Initializing Database' :
                 currentOperation === 'clear' ? 'Clearing Database' :
                 'Processing'}
                {operationProgress.stage && ` - ${operationProgress.stage.replace(/[-_]/g, ' ')}`}
              </span>
              <div className="flex items-center space-x-2">
                {operationProgress.processed !== undefined && operationProgress.total !== undefined && (
                  <span className="text-sm text-gray-600">
                    {operationProgress.processed.toLocaleString()} / {operationProgress.total.toLocaleString()}
                  </span>
                )}
                {operationProgress.successful !== undefined && (
                  <span className="text-xs text-green-600 font-medium">
                    ✓ {operationProgress.successful}
                  </span>
                )}
                {operationProgress.failed !== undefined && operationProgress.failed > 0 && (
                  <span className="text-xs text-red-600 font-medium">
                    ✗ {operationProgress.failed}
                  </span>
                )}
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div
                className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${operationProgress.percentage || 0}%` }}
              />
            </div>
            <div className="flex justify-between items-center mt-1">
              <div className="text-xs text-gray-600">
                {operationProgress.percentage || 0}% complete
              </div>
              {currentOperation && (
                <button
                  onClick={() => unifiedDataOps.cancelOperation()}
                  className="text-xs text-red-600 hover:text-red-800 flex items-center space-x-1"
                >
                  <X className="w-3 h-3" />
                  <span>Cancel</span>
                </button>
              )}
            </div>
          </div>
        )}

        {/* Unified Database Content */}
        <div className="flex-1 overflow-y-auto space-y-6">
          {/* Database Status Section */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <Database className="w-4 h-4 mr-2" />
                Database Status
              </h4>
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>SQLite Enabled:</span>
                  <span className={sqliteEnabled ? 'text-green-600' : 'text-red-600'}>
                    {sqliteEnabled ? 'Yes' : 'No'}
                  </span>
                </div>
                <div className="flex justify-between">
                  <span>Migration Status:</span>
                  <span className={migrationStatus === 'completed' ? 'text-green-600' : 'text-yellow-600'}>
                    {migrationStatus === 'completed' ? 'Complete' : 'Pending'}
                  </span>
                </div>
                {(databaseStats || enhancedStats) && (
                  <>
                    <div className="flex justify-between">
                      <span>Total Products:</span>
                      <span className="font-medium">
                        {enhancedStats?.sqlite?.totalProducts || databaseStats?.sqlite?.totalProducts || 0}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Database Size:</span>
                      <span className="font-medium">
                        {enhancedStats?.sqlite?.formattedSize || databaseStats?.sqlite?.formattedSize || 'Unknown'}
                      </span>
                    </div>
                  </>
                )}
              </div>
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                <BarChart3 className="w-4 h-4 mr-2" />
                Products by Category
              </h4>
              {enhancedStats?.sqlite?.productsByCategory ? (
                <div className="space-y-1 text-sm max-h-32 overflow-y-auto">
                  {enhancedStats.sqlite.productsByCategory.map((item, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="truncate">{item.category}</span>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-500 text-sm">No categories found</div>
              )}
            </div>
          </div>

          {/* Enhanced Data Summary */}
          <div className="bg-gray-50 rounded-lg p-4">
            <h4 className="font-medium text-gray-900 mb-3">Data Summary</h4>
            <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
              <div>
                <span className="text-gray-600">Categories:</span>
                <span className="text-gray-900 ml-2 font-medium">
                  {getAllCategories().length} ({customCategories.length} custom)
                </span>
              </div>
              <div>
                <span className="text-gray-600">Products:</span>
                <span className="text-gray-900 ml-2 font-medium">
                  {allProducts.filter(p => !(settings.deletedProducts || []).includes(p.id)).length} ({customProducts.length} custom)
                </span>
              </div>
              <div>
                <span className="text-gray-600">Storage:</span>
                <span className="text-gray-900 ml-2 font-medium">
                  {storageInfo?.type || 'localStorage'}
                </span>
              </div>
            </div>
          </div>

          {/* Database Operations */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Database Operations</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Initialize Database */}
              <button
                onClick={handleInitializeDatabase}
                disabled={currentOperation || sqliteEnabled}
                className="flex items-center justify-center space-x-2 p-3 bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Database className="w-4 h-4" />
                <span>{sqliteEnabled ? 'Database Ready' : 'Initialize Database'}</span>
              </button>

              {/* Export Data */}
              <button
                onClick={handleExportData}
                disabled={currentOperation}
                className="flex items-center justify-center space-x-2 p-3 bg-green-600 hover:bg-green-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Download className="w-4 h-4" />
                <span>Export All Data</span>
              </button>

              {/* Import Data */}
              <label className="flex items-center justify-center space-x-2 p-3 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors cursor-pointer">
                <Upload className="w-4 h-4" />
                <span>Import Data</span>
                <input
                  type="file"
                  accept=".json,.xlsx"
                  onChange={handleImportData}
                  className="hidden"
                  disabled={currentOperation}
                />
              </label>

              {/* Clear All Data */}
              <button
                onClick={confirmClearAllData}
                disabled={currentOperation}
                className="flex items-center justify-center space-x-2 p-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Clear All Data</span>
              </button>
            </div>
          </div>

          {/* Backup Management */}
          <div className="space-y-4">
            <h4 className="font-medium text-gray-900">Backup Management</h4>

            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <button
                onClick={handleCreateBackup}
                disabled={currentOperation}
                className="flex items-center justify-center space-x-2 p-3 bg-indigo-600 hover:bg-indigo-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Save className="w-4 h-4" />
                <span>Create Backup</span>
              </button>

              <button
                onClick={loadBackups}
                disabled={currentOperation}
                className="flex items-center justify-center space-x-2 p-3 bg-gray-600 hover:bg-gray-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <RefreshCw className="w-4 h-4" />
                <span>Refresh Backups</span>
              </button>

              <button
                onClick={() => setShowDeleteAllBackupsConfirm(true)}
                disabled={currentOperation || backups.length === 0}
                className="flex items-center justify-center space-x-2 p-3 bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded-lg transition-colors"
              >
                <Trash2 className="w-4 h-4" />
                <span>Delete All Backups</span>
              </button>
            </div>

            {/* Backup List */}
            {backups.length > 0 && (
              <div className="bg-white border rounded-lg">
                <div className="p-3 border-b bg-gray-50">
                  <h5 className="font-medium text-gray-900">Available Backups ({backups.length})</h5>
                </div>
                <div className="max-h-64 overflow-y-auto">
                  {backups.map((backup, index) => (
                    <div key={backup.id || index} className="p-3 border-b last:border-b-0 flex items-center justify-between">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{backup.name}</div>
                        <div className="text-sm text-gray-500">
                          {new Date(backup.timestamp).toLocaleString()}
                          {backup.size && ` • ${backup.size}`}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleRestoreBackup(backup)}
                          disabled={currentOperation}
                          className="px-3 py-1 text-sm bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white rounded transition-colors"
                        >
                          Restore
                        </button>
                        <button
                          onClick={() => handleDeleteBackup(backup)}
                          disabled={currentOperation}
                          className="px-3 py-1 text-sm bg-red-600 hover:bg-red-700 disabled:bg-gray-400 text-white rounded transition-colors"
                        >
                          Delete
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Render Categories Tab
  const renderCategoriesTab = () => {
    const allCategories = getAllCategories();

    return (
      <div className="p-4 flex flex-col h-full">
        <div className="mb-4 flex items-center">
          <input
            type="text"
            placeholder={t('Search categories...')}
            className="w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button
            onClick={() => resetCategoryVisibility()}
            className="ml-2 p-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            title={t('Reset visibility')}
          >
            <RotateCcw size={16} />
          </button>
        </div>

        <div className="overflow-y-auto flex-1">
          {allCategories.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 text-lg">{t('No categories available')}</p>
              <p className="text-gray-500 text-sm mt-2">{t('Add products to create categories automatically')}</p>
            </div>
          ) : (
            allCategories
              .filter(cat => !searchTerm || cat.name.toLowerCase().includes(searchTerm.toLowerCase()))
              .map((category) => {
                const isVisible = settings.visibleCategories[category.name] || false;
                const isRemoved = settings.removedCategories.includes(category.name);
                const isExpanded = expandedCategories[category.name] || false;

                return (
                  <div key={category.name} className={`mb-2 border border-gray-200 rounded ${isRemoved ? 'bg-gray-50' : 'bg-white'}`}>
                    <div className="p-3 flex items-center justify-between">
                      <div className="flex items-center flex-1">
                        <button
                          onClick={() => toggleCategoryExpanded(category.name)}
                          className="mr-2 p-1 rounded hover:bg-gray-100 text-gray-700"
                        >
                          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                        </button>

                        {/* Category name - editable when in edit mode */}
                        {editingCategory === category.name ? (
                          <div className="flex items-center flex-1 mr-2">
                            <input
                              type="text"
                              value={editCategoryForm.name}
                              onChange={(e) => handleEditCategoryFormChange('name', e.target.value)}
                              className={`flex-1 p-2 border rounded text-sm text-gray-900 bg-white ${
                                editCategoryErrors.name
                                  ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                              } focus:outline-none focus:ring-2 placeholder-gray-500`}
                              placeholder={t('Category name')}
                              autoFocus
                            />
                            <div className="flex items-center ml-2">
                              <button
                                onClick={handleSaveEditedCategory}
                                className="p-1 text-green-600 rounded hover:bg-green-100 bg-green-50 transition-colors mr-1"
                                title={t('Save changes')}
                              >
                                <CheckCircle size={16} />
                              </button>
                              <button
                                onClick={handleCancelEditCategory}
                                className="p-1 text-gray-600 rounded hover:bg-gray-100 bg-gray-50 transition-colors"
                                title={t('Cancel')}
                              >
                                <X size={16} />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <>
                            <span className={`font-medium ${isRemoved ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                              {category.name}
                            </span>
                            {category.isCustom && <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Custom</span>}
                          </>
                        )}

                        {/* Error message for category editing */}
                        {editingCategory === category.name && editCategoryErrors.name && (
                          <div className="ml-2 text-xs text-red-600">
                            {editCategoryErrors.name}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center">
                        {!isRemoved && editingCategory !== category.name ? (
                          <>
                            <button
                              onClick={() => handleEditCategory(category)}
                              className="mr-2 p-2 text-blue-600 rounded hover:bg-blue-100 bg-blue-50 transition-colors"
                              title={t('Edit category')}
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => toggleCategoryVisibility(category.name)}
                              className={`mr-2 p-2 rounded transition-colors ${isVisible ? 'text-green-600 hover:bg-green-100 bg-green-50' : 'text-red-600 hover:bg-red-100 bg-red-50'}`}
                              title={isVisible ? t('Hide category') : t('Show category')}
                            >
                              {isVisible ? <Eye size={16} /> : <EyeOff size={16} />}
                            </button>
                            <button
                              onClick={() => removeCategory(category.name)}
                              className="p-2 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                              title={t('Remove category')}
                            >
                              <Trash2 size={16} />
                            </button>
                          </>
                        ) : !isRemoved && editingCategory === category.name ? (
                          // Hide other buttons when editing
                          null
                        ) : (
                          <>
                            <button
                              onClick={() => restoreCategory(category.name)}
                              className="mr-2 p-2 text-green-600 rounded hover:bg-green-100 bg-green-50 transition-colors"
                              title={t('Restore category')}
                            >
                              <RotateCcw size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteCategory(category.name)}
                              className="p-2 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                              title={t('Delete permanently')}
                            >
                              <Trash2 size={16} />
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    {isExpanded && (
                      <div className="pl-8 pr-3 pb-3 border-t border-gray-100">
                        {category.subcategories.map((subcategory) => {
                          const isSubcategoryRemoved = settings.removedSubcategories.some(
                            item => item.category === category.name && item.subcategory === subcategory
                          );

                          return (
                            <div key={subcategory} className="flex items-center justify-between py-2 border-b border-gray-50 last:border-b-0">
                              <span className={`text-sm ${isSubcategoryRemoved ? 'text-gray-500 line-through' : 'text-gray-700'}`}>
                                {subcategory}
                              </span>
                              {!isSubcategoryRemoved ? (
                                <button
                                  onClick={() => removeSubcategory(category.name, subcategory)}
                                  className="p-1 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                                  title={t('Remove subcategory')}
                                >
                                  <Trash2 size={14} />
                                </button>
                              ) : (
                                <div className="flex">
                                  <button
                                    onClick={() => restoreSubcategory(category.name, subcategory)}
                                    className="p-1 text-green-600 rounded hover:bg-green-100 bg-green-50 transition-colors mr-1"
                                    title={t('Restore subcategory')}
                                  >
                                    <RotateCcw size={14} />
                                  </button>
                                  <button
                                    onClick={() => handleDeleteSubcategory(category.name, subcategory)}
                                    className="p-1 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                                    title={t('Delete permanently')}
                                  >
                                    <Trash2 size={14} />
                                  </button>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })
          )}
        </div>
      </div>
    );
  };

  // Render Products Tab
  const renderProductsTab = () => {
    return (
      <div className="p-4 flex flex-col h-full">
        <div className="mb-4">
          <input
            type="text"
            placeholder={t('Search products...')}
            className="w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="overflow-y-auto flex-1">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 text-lg">{t('No products available')}</p>
              <p className="text-gray-500 text-sm mt-2">{t('Use the "Add Product" button to create your first product')}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filteredProducts.map((product) => {
                return (
                  <div
                    key={product.id}
                    className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-bold text-sm text-gray-900">{product.name}</h3>
                        <p className="text-xs text-gray-600">{product.category} &gt; {product.subcategory}</p>
                      </div>
                      <div className="flex space-x-1">
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="p-2 text-blue-600 rounded hover:bg-blue-100 bg-blue-50 transition-colors"
                          title={t('Edit Product')}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => permanentlyDeleteProduct(product.id)}
                          className="p-2 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                          title={t('Delete product')}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gray-100 border border-gray-200 rounded mr-3 flex-shrink-0 flex items-center justify-center overflow-hidden">
                        {product.image ? (
                          <img src={product.image} alt={product.name} className="object-contain max-w-full max-h-full" />
                        ) : (
                          <Package size={20} className="text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-gray-700">
                          {t('Price')}: <span className="text-green-600 font-bold">${product.price?.toFixed(2) || '0.00'}</span>
                          {product.quantity && product.quantity > 1 && (
                            <span className="ml-2 text-orange-600 font-bold">Qty: {product.quantity}</span>
                          )}
                        </p>
                        <p className="text-xs text-gray-600">
                          {t('Size')}: {product.diameter || product.size || 'N/A'}
                        </p>
                        {product.material && (
                          <p className="text-xs text-gray-600">
                            {t('Material')}: {product.material}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };





  // Missing functions that were accidentally removed
  const handleExportData = async () => {
    try {
      setCurrentOperation('export');
      setDataOperationStatus({
        type: 'info',
        message: 'Exporting data...'
      });

      const result = await unifiedDataOps.exportData({
        enableChunking: true,
        includeImages: true,
        onProgress: (progress) => {
          setOperationProgress(progress);
        },
        onStatusUpdate: (status) => {
          setDataOperationStatus(status);
        }
      });

      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: 'Data exported successfully'
        });
      } else {
        setDataOperationStatus({
          type: 'error',
          message: 'Export failed: ' + result.message
        });
      }
    } catch (error) {
      setDataOperationStatus({
        type: 'error',
        message: 'Export failed: ' + error.message
      });
    } finally {
      setCurrentOperation(null);
      setOperationProgress(null);
    }
  };

  const handleImportData = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setCurrentOperation('import');
      setDataOperationStatus({
        type: 'info',
        message: `Importing data from "${file.name}"...`
      });

      const result = await unifiedDataOps.importData(file, {
        enableChunking: true,
        validateData: true,
        onLoadProject: onLoadProject,
        updateSettings: updateSettings,
        importSettings: importSettings,
        saveApplicationData: saveApplicationData,
        onProgress: (progress) => {
          setOperationProgress(progress);
        },
        onStatusUpdate: (status) => {
          setDataOperationStatus(status);
        }
      });

      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: `Data imported successfully from "${file.name}"`
        });
        if (onCategoryChanges) {
          onCategoryChanges();
        }
      } else {
        setDataOperationStatus({
          type: 'error',
          message: 'Import failed: ' + result.message
        });
      }
    } catch (error) {
      setDataOperationStatus({
        type: 'error',
        message: 'Import failed: ' + error.message
      });
    } finally {
      setCurrentOperation(null);
      setOperationProgress(null);
      event.target.value = '';
    }
  };

  const handleCreateBackup = async () => {
    try {
      setCurrentOperation('backup');
      setDataOperationStatus({
        type: 'info',
        message: 'Creating backup...'
      });

      const result = await saveApplicationData();
      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: 'Backup created successfully'
        });
        setBackups(getBackups());
      } else {
        setDataOperationStatus({
          type: 'error',
          message: 'Failed to create backup'
        });
      }
    } catch (error) {
      setDataOperationStatus({
        type: 'error',
        message: 'Error creating backup: ' + error.message
      });
    } finally {
      setCurrentOperation(null);
    }
  };

  const loadBackups = () => {
    setBackups(getBackups());
    setDataOperationStatus({
      type: 'success',
      message: 'Backups refreshed'
    });
  };
  const handleRestoreBackup = async (backupKey) => {
    setIsDataLoading(true);
    try {
      const result = restoreFromBackup(backupKey);
      if (result.success && result.data) {
        // Update application state with restored data
        if (result.data.canvasProducts && Array.isArray(result.data.canvasProducts) && onLoadProject) {
          onLoadProject({
            products: result.data.canvasProducts,
            connections: result.data.connections || [],
            selectedProducts: result.data.selectedProducts || []
          });
        }

        // Save the restored data to localStorage for persistence
        try {
          const saveResult = await saveApplicationData(result.data);
          if (saveResult.success) {
            console.log('Restored data saved to localStorage successfully');
          }
        } catch (saveError) {
          console.warn('Error saving restored data to localStorage:', saveError);
        }

        // Trigger UI refresh
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        setDataOperationStatus({
          type: 'success',
          message: 'Backup restored successfully'
        });

        // Refresh backup list after restore
        setBackups(getBackups());

        // Close the modal after successful restore to let user see the changes
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        throw new Error(result.message || 'Failed to restore backup');
      }
    } catch (error) {
      console.error('Restore backup error:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Restore failed: ' + error.message
      });
    } finally {
      setIsDataLoading(false);
    }
  };

  const confirmDeleteBackup = (backup) => {
    setBackupToDelete(backup);
    setShowDeleteBackupConfirm(true);
  };

  const handleDeleteBackup = async () => {
    if (!backupToDelete) return;

    setIsDataLoading(true);
    try {
      const result = deleteBackup(backupToDelete.key);
      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: 'Backup deleted successfully'
        });
        // Refresh backup list after deletion
        setBackups(getBackups());
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      setDataOperationStatus({
        type: 'error',
        message: 'Delete failed: ' + error.message
      });
    } finally {
      setIsDataLoading(false);
      setShowDeleteBackupConfirm(false);
      setBackupToDelete(null);
    }
  };

  const cancelDeleteBackup = () => {
    setShowDeleteBackupConfirm(false);
    setBackupToDelete(null);
  };

  // Bulk delete all backups functionality
  const confirmDeleteAllBackups = () => {
    if (backups.length === 0) {
      setDataOperationStatus({
        type: 'info',
        message: 'No backups available to delete'
      });
      return;
    }
    setShowDeleteAllBackupsConfirm(true);
  };

  const handleDeleteAllBackups = async () => {
    if (backups.length === 0) return;

    setIsDeletingAllBackups(true);
    try {
      const backupKeys = backups.map(backup => backup.key);
      const result = deleteMultipleBackups(backupKeys);

      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: `All ${result.deletedCount} backup(s) deleted successfully`
        });
        // Refresh backup list after deletion
        setBackups(getBackups());
      } else {
        setDataOperationStatus({
          type: 'error',
          message: `Failed to delete some backups: ${result.errors.join(', ')}`
        });
        // Still refresh the list to show what was actually deleted
        setBackups(getBackups());
      }
    } catch (error) {
      console.error('Error deleting all backups:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Delete all backups failed: ' + error.message
      });
    } finally {
      setIsDeletingAllBackups(false);
      setShowDeleteAllBackupsConfirm(false);
    }
  };

  const cancelDeleteAllBackups = () => {
    setShowDeleteAllBackupsConfirm(false);
  };



  const toggleAutoSave = async () => {
    const newSettings = {
      autoSave: !autoSaveEnabled,
      autoLoad: true,
      backupInterval: 300000,
      maxBackups: 10
    };

    const result = saveDataSettings(newSettings);
    if (result.success) {
      setDataOperationStatus({
        type: 'success',
        message: `Auto-save ${newSettings.autoSave ? 'enabled' : 'disabled'}`
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Load backups when database tab is opened
  React.useEffect(() => {
    if (activeTab === 'database') {
      setBackups(getBackups());
      // Clear any previous status messages when switching to database tab
      setDataOperationStatus(null);
      // Setup unified operations and load enhanced stats
      setupUnifiedOperations();
      loadEnhancedStats();
    }
  }, [activeTab, getBackups]);

  // Setup unified operations callbacks
  const setupUnifiedOperations = () => {
    unifiedDataOps.setCallbacks(
      (progress) => setOperationProgress(progress),
      (status) => setDataOperationStatus(status)
    );
  };

  // Load enhanced database statistics
  const loadEnhancedStats = async () => {
    try {
      setIsDataLoading(true);
      const result = await enhancedDataStorage.getStorageStats();
      if (result.success) {
        setEnhancedStats(result.stats);
      }
    } catch (error) {
      console.error('Error loading enhanced stats:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Error loading database statistics'
      });
    } finally {
      setIsDataLoading(false);
    }
  };

  // Clear status messages after 5 seconds
  React.useEffect(() => {
    if (dataOperationStatus) {
      const timer = setTimeout(() => {
        setDataOperationStatus(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [dataOperationStatus]);

  // Database operation functions
  const handleInitializeDatabase = async () => {
    try {
      setIsDataLoading(true);
      setCurrentOperation('initialize');
      setDataOperationStatus({
        type: 'info',
        message: 'Initializing enhanced database storage...'
      });

      // Update progress
      setOperationProgress({
        processed: 0,
        total: 100,
        percentage: 0,
        stage: 'starting'
      });

      console.log('Starting database initialization...');

      // Initialize enhanced storage system
      const result = await enhancedDataStorage.initialize();

      setOperationProgress({
        processed: 50,
        total: 100,
        percentage: 50,
        stage: 'initializing'
      });

      if (result.success) {
        console.log('Database initialization successful:', result);

        // Update progress
        setOperationProgress({
          processed: 80,
          total: 100,
          percentage: 80,
          stage: 'loading-stats'
        });

        // Refresh database statistics
        await loadEnhancedStats();
        if (refreshDatabaseStats) {
          await refreshDatabaseStats();
        }

        // Also initialize the DataContext enhanced storage
        if (initializeEnhancedStorage) {
          await initializeEnhancedStorage();
        }

        setOperationProgress({
          processed: 100,
          total: 100,
          percentage: 100,
          stage: 'complete'
        });

        const successMessage = result.sqliteEnabled
          ? `Database initialized successfully with SQLite support. ${result.message}`
          : `Database initialized with localStorage fallback. ${result.message}`;

        setDataOperationStatus({
          type: 'success',
          message: successMessage
        });

        console.log('Database initialization completed successfully');

      } else {
        console.error('Database initialization failed:', result);
        setDataOperationStatus({
          type: 'error',
          message: `Initialization failed: ${result.message}`
        });
      }
    } catch (error) {
      console.error('Error during database initialization:', error);
      setDataOperationStatus({
        type: 'error',
        message: `Initialization failed: ${error.message}`
      });
    } finally {
      setIsDataLoading(false);
      setCurrentOperation(null);
      setOperationProgress(null);
    }
  };

  const handleDatabaseExport = async () => {
    try {
      setIsDataLoading(true);
      setCurrentOperation('export');
      setDataOperationStatus({
        type: 'info',
        message: 'Preparing database export...'
      });

      // Update progress
      setOperationProgress({
        processed: 0,
        total: 100,
        percentage: 0,
        stage: 'preparing'
      });

      console.log('Starting database export...');

      // Generate filename with timestamp
      const timestamp = new Date().toISOString().split('T')[0];
      const timeString = new Date().toTimeString().split(' ')[0].replace(/:/g, '-');
      const filename = `plombdesign-database-${timestamp}-${timeString}.json`;

      // Export data using unified operations
      const result = await unifiedDataOps.exportData({
        filename: filename,
        enableChunking: true,
        includeMetadata: true,
        validateData: true
      });

      if (result.success) {
        console.log('Database export successful:', result);

        // Update progress to completion
        setOperationProgress({
          processed: 100,
          total: 100,
          percentage: 100,
          stage: 'complete'
        });

        // Show detailed success message
        const successMessage = `Database exported successfully as "${result.filename}". ` +
          `${result.productCount || 0} products exported (${result.statistics?.totalProducts || 0} total items). ` +
          `File size: ${result.size ? Math.round(result.size / 1024) + ' KB' : 'Unknown'}`;

        setDataOperationStatus({
          type: 'success',
          message: successMessage
        });

        // Refresh statistics
        await loadEnhancedStats();
        if (refreshDatabaseStats) {
          await refreshDatabaseStats();
        }

        console.log('Database export completed successfully');

      } else {
        console.error('Database export failed:', result);
        setDataOperationStatus({
          type: 'error',
          message: `Export failed: ${result.message || 'Unknown error occurred'}`
        });
      }
    } catch (error) {
      console.error('Error during database export:', error);
      setDataOperationStatus({
        type: 'error',
        message: `Export failed: ${error.message}`
      });
    } finally {
      setIsDataLoading(false);
      setCurrentOperation(null);
      setOperationProgress(null);
    }
  };

  const handleDatabaseImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setIsDataLoading(true);
      setCurrentOperation('import');
      setDataOperationStatus({
        type: 'info',
        message: 'Processing import file...'
      });

      setOperationProgress({
        processed: 0,
        total: 100,
        percentage: 0,
        stage: 'reading-file'
      });

      console.log('Starting database import...');

      // Import data using unified operations
      const result = await unifiedDataOps.importData(file, {
        enableChunking: true,
        validateData: true,
        mergeStrategy: 'replace'
      });

      if (result.success) {
        console.log('Database import successful:', result);

        setOperationProgress({
          processed: 100,
          total: 100,
          percentage: 100,
          stage: 'complete'
        });

        const successMessage = `Database imported successfully. ` +
          `${result.productCount || 0} products imported. ` +
          `${result.statistics?.totalProducts || 0} total items in database.`;

        setDataOperationStatus({
          type: 'success',
          message: successMessage
        });

        // Refresh statistics and UI
        await loadEnhancedStats();
        if (refreshDatabaseStats) {
          await refreshDatabaseStats();
        }
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        console.log('Database import completed successfully');

      } else {
        console.error('Database import failed:', result);
        setDataOperationStatus({
          type: 'error',
          message: `Import failed: ${result.message || 'Unknown error occurred'}`
        });
      }
    } catch (error) {
      console.error('Error during database import:', error);
      setDataOperationStatus({
        type: 'error',
        message: `Import failed: ${error.message}`
      });
    } finally {
      setIsDataLoading(false);
      setCurrentOperation(null);
      setOperationProgress(null);
      event.target.value = '';
    }
  };

  const handleClearDatabase = async () => {
    try {
      setIsDataLoading(true);
      setCurrentOperation('clear');
      setDataOperationStatus({
        type: 'info',
        message: 'Clearing database...'
      });

      setOperationProgress({
        processed: 0,
        total: 100,
        percentage: 0,
        stage: 'clearing'
      });

      console.log('Starting database clear...');

      // Clear data using unified operations
      const result = await unifiedDataOps.clearAllData();

      if (result.success) {
        console.log('Database clear successful:', result);

        setOperationProgress({
          processed: 100,
          total: 100,
          percentage: 100,
          stage: 'complete'
        });

        setDataOperationStatus({
          type: 'success',
          message: `Database cleared successfully. ${result.message}`
        });

        // Refresh statistics and UI
        await loadEnhancedStats();
        if (refreshDatabaseStats) {
          await refreshDatabaseStats();
        }
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        console.log('Database clear completed successfully');

      } else {
        console.error('Database clear failed:', result);
        setDataOperationStatus({
          type: 'error',
          message: `Clear failed: ${result.message || 'Unknown error occurred'}`
        });
      }
    } catch (error) {
      console.error('Error during database clear:', error);
      setDataOperationStatus({
        type: 'error',
        message: `Clear failed: ${error.message}`
      });
    } finally {
      setIsDataLoading(false);
      setCurrentOperation(null);
      setOperationProgress(null);
    }
  };



  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col overflow-hidden border-2 border-gray-200">
        {/* Modal header with tabs */}
        <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
          <div className="flex items-center">
            <Database className="mr-2 text-blue-600" size={20} />
            <h2 className="text-xl font-bold text-gray-800">{t('settings')}</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-red-100 text-red-600 transition-colors duration-200"
            title={t('Close')}
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs navigation */}
        <div className="flex border-b bg-gray-50">
          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'categories'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('categories')}
          >
            <div className="flex items-center">
              <Tag size={16} className="mr-2" />
              {t('Categories')}
            </div>
          </button>
          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'products'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('products')}
          >
            <div className="flex items-center">
              <Package size={16} className="mr-2" />
              {t('Products')}
            </div>
          </button>

          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'settings'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('settings')}
          >
            <div className="flex items-center">
              <Database size={16} className="mr-2" />
              {t('Settings')}
            </div>
          </button>

          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'database'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('database')}
          >
            <div className="flex items-center">
              <Database size={16} className="mr-2" />
              {t('Database')}
            </div>
          </button>

        </div>

        {/* Tab content */}
        <div className="flex-1 overflow-hidden">
          {renderTabContent()}
        </div>

        {/* Bottom action bar */}
        <div className="p-4 border-t bg-gray-50 flex justify-end">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200 flex items-center"
          >
            <X size={16} className="mr-2" />
            {t('Exit')}
          </button>
        </div>

        {/* Standalone Product Template Builder */}
        <ProductTemplateBuilder
          isOpen={showStandaloneTemplateBuilder}
          onClose={() => setShowStandaloneTemplateBuilder(false)}
        />



        {/* Confirmation dialog */}
        {confirmationDialogOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
              <h3 className="font-bold mb-4 text-gray-900 text-lg">{t('Confirm Action')}</h3>
              <p className="mb-6 text-gray-700">{t('Are you sure you want to proceed? This action cannot be undone.')}</p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setConfirmationDialogOpen(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 transition-colors"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={confirmationAction}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                >
                  {t('Confirm')}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Deletion confirmation modal */}
        {showDeleteConfirm && deleteTarget && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-lg mx-4">
              <div className="flex items-center mb-4">
                <AlertCircle className="text-red-600 mr-3" size={24} />
                <h3 className="font-bold text-gray-900 text-lg">
                  {t('Delete')} {deleteType === 'category' ? t('Category') : t('Subcategory')}
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-900 mb-3 font-medium">
                  {deleteType === 'category'
                    ? t('Are you sure you want to permanently delete the category')
                    : t('Are you sure you want to permanently delete the subcategory')
                  } <strong className="text-black">"{deleteTarget.name}"</strong>?
                </p>

                {deleteTarget.productCount > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
                    <p className="text-yellow-800 font-medium">
                      ⚠️ {t('Warning')}: {deleteTarget.productCount} {deleteTarget.productCount === 1 ? t('product') : t('products')}
                      {deleteType === 'category'
                        ? ` ${t('in this category will be affected')}`
                        : ` ${t('in this subcategory will be affected')}`
                      }
                    </p>
                  </div>
                )}

                <div className="space-y-3">
                  <p className="font-medium text-black">{t('What should happen to the affected products?')}</p>

                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="productHandling"
                        value="orphan"
                        checked={productHandlingOption === 'orphan'}
                        onChange={(e) => setProductHandlingOption(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-900 font-medium">
                        {deleteType === 'category'
                          ? t('Move products to "Uncategorized"')
                          : t('Move products to "Other" subcategory')
                        }
                      </span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="productHandling"
                        value="delete"
                        checked={productHandlingOption === 'delete'}
                        onChange={(e) => setProductHandlingOption(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-red-600 font-medium">
                        {t('Delete all products permanently')}
                      </span>
                    </label>

                    {deleteTarget.productCount > 0 && (
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="productHandling"
                          value="reassign"
                          checked={productHandlingOption === 'reassign'}
                          onChange={(e) => setProductHandlingOption(e.target.value)}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-900 font-medium">
                          {deleteType === 'category'
                            ? t('Reassign products to another category')
                            : t('Reassign products to another subcategory')
                          }
                        </span>
                      </label>
                    )}
                  </div>

                  {productHandlingOption === 'reassign' && (
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                      {deleteType === 'category' ? (
                        <div>
                          <label className="block text-sm font-medium text-black mb-1">
                            {t('Select target category')}:
                          </label>
                          <select
                            value={reassignTarget.category}
                            onChange={(e) => setReassignTarget(prev => ({
                              ...prev,
                              category: e.target.value,
                              subcategory: ''
                            }))}
                            className="w-full p-2 border border-gray-300 rounded text-sm text-gray-900"
                          >
                            <option value="">{t('Select category')}</option>
                            {getAvailableCategories(deleteTarget.name).map(cat => (
                              <option key={cat.name} value={cat.name}>{cat.name}</option>
                            ))}
                          </select>
                          {reassignTarget.category && (
                            <div className="mt-2">
                              <label className="block text-sm font-medium text-black mb-1">
                                {t('Select target subcategory')}:
                              </label>
                              <select
                                value={reassignTarget.subcategory}
                                onChange={(e) => setReassignTarget(prev => ({
                                  ...prev,
                                  subcategory: e.target.value
                                }))}
                                className="w-full p-2 border border-gray-300 rounded text-sm text-gray-900"
                              >
                                <option value="">{t('Select subcategory')}</option>
                                {getAvailableSubcategories(reassignTarget.category).map(subcat => (
                                  <option key={subcat} value={subcat}>{subcat}</option>
                                ))}
                              </select>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div>
                          <label className="block text-sm font-medium text-black mb-1">
                            {t('Select target subcategory')}:
                          </label>
                          <select
                            value={reassignTarget.subcategory}
                            onChange={(e) => setReassignTarget(prev => ({
                              ...prev,
                              subcategory: e.target.value
                            }))}
                            className="w-full p-2 border border-gray-300 rounded text-sm text-gray-900"
                          >
                            <option value="">{t('Select subcategory')}</option>
                            {getAvailableSubcategories(deleteTarget.category, deleteTarget.name).map(subcat => (
                              <option key={subcat} value={subcat}>{subcat}</option>
                            ))}
                          </select>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {deleteError && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                    <p className="text-red-800 text-sm">{deleteError}</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleCancelDeletion}
                  disabled={isDeleting}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded transition-colors"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={handleConfirmDeletion}
                  disabled={isDeleting || (productHandlingOption === 'reassign' && (
                    deleteType === 'category' ? !reassignTarget.category : !reassignTarget.subcategory
                  ))}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded transition-colors flex items-center space-x-2"
                >
                  {isDeleting ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>{t('Deleting...')}</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      <span>{t('Delete Permanently')}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Clear All Data Confirmation Dialog */}
        {showClearAllDialog && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
              <div className="flex items-center mb-4">
                <AlertTriangle className="text-red-600 mr-3" size={24} />
                <h3 className="font-bold text-gray-900 text-lg">{t('Clear All Data')}</h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-700 mb-4">
                  {t('Are you sure you want to delete all application data? This will remove:')}
                </p>

                <ul className="text-sm text-gray-600 space-y-1 mb-4">
                  <li>• {t('All canvas products and connections')}</li>
                  <li>• {t('All custom products and categories')}</li>
                  <li>• {t('All settings and preferences')}</li>
                  <li>• {t('All backup files')}</li>
                  <li>• {t('All stored images and data')}</li>
                </ul>

                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-red-800 text-sm font-medium">
                    ⚠️ {t('Warning')}: {t('This action cannot be undone and you will lose all your work.')}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowClearAllDialog(false)}
                  disabled={isClearing}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded transition-colors"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={handleClearAllData}
                  disabled={isClearing}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded transition-colors flex items-center space-x-2"
                >
                  {isClearing ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>{t('Clearing...')}</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      <span>{t('Clear All Data')}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Backup deletion confirmation modals */}
        {showDeleteBackupConfirm && backupToDelete && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
              <div className="flex items-center mb-4">
                <AlertCircle className="text-red-600 mr-3" size={24} />
                <h3 className="font-bold text-gray-900 text-lg">{t('Delete Backup')}</h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-700 mb-4">
                  {t('Are you sure you want to delete this backup?')}
                </p>
                <div className="bg-gray-50 border border-gray-200 rounded p-3">
                  <p className="text-gray-900 font-medium">{formatDate(backupToDelete.timestamp)}</p>
                  <p className="text-gray-600 text-sm">
                    {t('Size')}: {Math.round(backupToDelete.size / 1024)} KB
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteBackup}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 rounded transition-colors"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={handleDeleteBackup}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 text-white rounded transition-colors flex items-center space-x-2"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>{t('Delete')}</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {showDeleteAllBackupsConfirm && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
              <div className="flex items-center mb-4">
                <AlertTriangle className="text-red-600 mr-3" size={24} />
                <h3 className="font-bold text-gray-900 text-lg">{t('Delete All Backups')}</h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-700 mb-4">
                  {t('Are you sure you want to delete all')} <strong>{backups.length} {t('backup(s)')}</strong>?
                  {t('This action cannot be undone and you will lose all backup history.')}
                </p>

                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-red-800 text-sm font-medium">
                    ⚠️ {t('Warning')}: {t('This will permanently delete all backup files. You will not be able to restore any previous versions of your work.')}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteAllBackups}
                  disabled={isDeletingAllBackups}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded transition-colors"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={handleDeleteAllBackups}
                  disabled={isDeletingAllBackups}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded transition-colors flex items-center space-x-2"
                >
                  {isDeletingAllBackups ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>{t('Deleting...')}</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      <span>{t('Delete All')} {backups.length} {t('Backups')}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default SettingsModal;
