import React, { useState, useMemo, useEffect, useCallback } from 'react';
import {
  X,
  <PERSON>ting<PERSON>,
  Eye,
  EyeOff,
  Save,
  Upload,
  Download,
  Trash2,
  RotateCcw,
  Grid,
  Clock,
  Shield,
  Palette,
  Database,

  Minus,
  Plus,
  ChevronDown,
  ChevronRight,
  Package,
  Tag,
  Search,
  History,
  Edit,
  FileSpreadsheet,
  AlertCircle,
  AlertTriangle,
  RefreshCw,
  CheckCircle,
  HardDrive
} from 'lucide-react';
import * as XLSX from 'xlsx';
import { useLanguage } from '../contexts/LanguageContext';
import { useSettings } from '../contexts/SettingsContext';
import { useData } from '../contexts/DataContext';
import { productCategories, defaultProducts } from '../data/products';
import { searchProducts } from '../utils/searchUtils';
import { clearProductImageCache } from '../utils/productImageUtils';
import ProductTemplateBuilder from './ProductTemplateBuilder';
import DatabaseManagementPanel from './DatabaseManagementPanel';

const SettingsModal = ({
  isOpen,
  onClose,
  canvasProducts,
  connections,
  onCategoryChanges,
  onLoadProject
}) => {
  const { t, language, setLanguage } = useLanguage();
  const {
    settings,
    customProducts,
    customCategories,

    getAllCategories,
    ensureCategoryExists,
    updateSetting,
    updateCategoryVisibility,
    resetCategoryVisibility,
    removeCategory,
    restoreCategory,
    permanentlyDeleteCategory,
    removeSubcategory,
    restoreSubcategory,
    permanentlyDeleteSubcategory,
    permanentlyDeleteProduct,
    getCategoryProductCount,
    getSubcategoryProductCount,
    getAvailableCategories,
    getAvailableSubcategories,
    reassignProducts,
    addCustomProduct,
    updateCustomProduct,

    clearAllData,
    clearCategoriesData,
    resetSettings,
    getDataSummary,
    restoreDefaultData,
    importSettings,
    updateSettings

  } = useSettings();

  // Data management context
  const {
    storageInfo,
    lastSaveTime,
    autoSaveEnabled,
    hasLoadedData,
    saveApplicationData,
    exportData,
    importData,
    getBackups,
    restoreFromBackup,
    deleteBackup,
    deleteMultipleBackups,
    clearAllData: clearDataStorage,
    saveSettings: saveDataSettings,
    // Enhanced storage
    sqliteEnabled,
    migrationStatus,
    databaseStats,
    saveProductToDatabase,
    getProductsFromDatabase,
    searchProductsInDatabase,
    refreshDatabaseStats,
    initializeEnhancedStorage
  } = useData();

  // State for managing tabs and UI
  const [activeTab, setActiveTab] = useState('categories');
  const [searchTerm, setSearchTerm] = useState('');
  const [expandedCategories, setExpandedCategories] = useState({});

  const [confirmationDialogOpen, setConfirmationDialogOpen] = useState(false);
  const [confirmationAction, setConfirmationAction] = useState(null);
  const [editingItem, setEditingItem] = useState(null);

  // State for Excel functionality
  const [isExporting, setIsExporting] = useState(false);
  const [importStatus, setImportStatus] = useState(null);

  // State for enhanced clear all data dialog
  const [showClearAllDialog, setShowClearAllDialog] = useState(false);
  const [isClearing, setIsClearing] = useState(false);

  // State for product editing
  const [editingProduct, setEditingProduct] = useState(null);
  const [showEditProductModal, setShowEditProductModal] = useState(false);
  const [editProductForm, setEditProductForm] = useState({
    name: '',
    category: '',
    subcategory: '',
    price: '',
    size: '',
    material: '',
    image: ''
  });
  const [editProductErrors, setEditProductErrors] = useState({});
  const [isUpdatingProduct, setIsUpdatingProduct] = useState(false);

  // State for image editing
  const [imagePreview, setImagePreview] = useState('');
  const [isProcessingImage, setIsProcessingImage] = useState(false);
  const [imageUploadError, setImageUploadError] = useState('');

  // State for category editing
  const [editingCategory, setEditingCategory] = useState(null);
  const [editCategoryForm, setEditCategoryForm] = useState({
    name: '',
    description: ''
  });
  const [editCategoryErrors, setEditCategoryErrors] = useState({});

  // State for Standalone Product Template Builder
  const [showStandaloneTemplateBuilder, setShowStandaloneTemplateBuilder] = useState(false);

  // State for Database Management Panel
  const [showDatabasePanel, setShowDatabasePanel] = useState(false);

  // State for deletion confirmation modals
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [deleteTarget, setDeleteTarget] = useState(null);
  const [deleteType, setDeleteType] = useState(''); // 'category' or 'subcategory'
  const [productHandlingOption, setProductHandlingOption] = useState('orphan');
  const [reassignTarget, setReassignTarget] = useState({ category: '', subcategory: '' });
  const [isDeleting, setIsDeleting] = useState(false);
  const [deleteError, setDeleteError] = useState('');

  // State for data storage management
  const [dataStorageTab, setDataStorageTab] = useState('overview');
  const [backups, setBackups] = useState([]);
  const [isDataLoading, setIsDataLoading] = useState(false);
  const [dataOperationStatus, setDataOperationStatus] = useState(null);
  const [showDeleteBackupConfirm, setShowDeleteBackupConfirm] = useState(false);
  const [backupToDelete, setBackupToDelete] = useState(null);
  const [showDeleteAllBackupsConfirm, setShowDeleteAllBackupsConfirm] = useState(false);
  const [isDeletingAllBackups, setIsDeletingAllBackups] = useState(false);

  // Combine default and custom products
  const allProducts = useMemo(() => {
    return [...defaultProducts, ...customProducts];
  }, [customProducts]);

  // Initialize expanded categories
  useEffect(() => {
    if (productCategories.length > 0 && Object.keys(expandedCategories).length === 0) {
      setExpandedCategories(
        productCategories.reduce((acc, category) => ({
          ...acc,
          [category.name]: false
        }), {})
      );
    }
  }, []);

  // Effect to handle tab initialization
  useEffect(() => {
    if (isOpen) {
      // Reset states when modal opens
      setSearchTerm('');
      setConfirmationDialogOpen(false);
      setConfirmationAction(null);
      setEditingItem(null);
    }
  }, [isOpen]);

  // Filter products by search term and category/product deletion status
  const filteredProducts = useMemo(() => {
    let filtered = allProducts;

    // Filter by search term using enhanced search utility
    if (searchTerm && searchTerm.trim()) {
      filtered = searchProducts(filtered, searchTerm);
    }

    // Filter out products from deleted categories
    filtered = filtered.filter(product =>
      !(settings.deletedCategories || []).includes(product.category)
    );

    // Filter out products from deleted subcategories
    filtered = filtered.filter(product => {
      const isSubcategoryDeleted = (settings.deletedSubcategories || []).some(
        item => item.category === product.category && item.subcategory === product.subcategory
      );
      return !isSubcategoryDeleted;
    });

    // Filter out individually deleted products
    filtered = filtered.filter(product =>
      !(settings.deletedProducts || []).includes(product.id)
    );

    return filtered;
  }, [allProducts, searchTerm, settings.deletedCategories, settings.deletedSubcategories, settings.deletedProducts]);



  // Toggle category visibility
  const toggleCategoryVisibility = (categoryName) => {
    updateCategoryVisibility(categoryName, !settings.visibleCategories[categoryName]);
    if (onCategoryChanges) {
      onCategoryChanges();
    }
  };

  // Toggle category expansion in the UI
  const toggleCategoryExpanded = (categoryName) => {
    setExpandedCategories(prev => ({
      ...prev,
      [categoryName]: !prev[categoryName]
    }));
  };

  // Enhanced clear all data with detailed confirmation
  const confirmClearAllData = () => {
    setShowClearAllDialog(true);
  };

  // Product editing functions
  const handleEditProduct = (product) => {
    setEditingProduct(product);
    setEditProductForm({
      name: product.name || '',
      category: product.category || '',
      subcategory: product.subcategory || '',
      price: product.price || '',
      size: product.size || product.diameter || '',
      material: product.material || '',
      image: product.image || ''
    });
    setEditProductErrors({});
    setImagePreview(product.image || '');
    setImageUploadError('');
    setShowEditProductModal(true);
  };

  const handleEditProductFormChange = (field, value) => {
    setEditProductForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (editProductErrors[field]) {
      setEditProductErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateEditProductForm = () => {
    const errors = {};

    if (!editProductForm.name.trim()) {
      errors.name = t('Product name is required') || 'Product name is required';
    }

    if (!editProductForm.category.trim()) {
      errors.category = t('Category is required') || 'Category is required';
    }

    if (!editProductForm.subcategory.trim()) {
      errors.subcategory = t('Subcategory is required') || 'Subcategory is required';
    }

    if (editProductForm.price && isNaN(parseFloat(editProductForm.price))) {
      errors.price = t('Price must be a valid number') || 'Price must be a valid number';
    }

    return errors;
  };

  const handleSaveEditedProduct = async () => {
    const errors = validateEditProductForm();

    if (Object.keys(errors).length > 0) {
      setEditProductErrors(errors);
      return;
    }

    setIsUpdatingProduct(true);

    try {
      const updatedProduct = {
        ...editingProduct,
        name: editProductForm.name.trim(),
        category: editProductForm.category.trim(),
        subcategory: editProductForm.subcategory.trim(),
        price: editProductForm.price ? parseFloat(editProductForm.price) : editingProduct.price,
        size: editProductForm.size.trim() || undefined,
        diameter: editProductForm.size.trim() || undefined,
        material: editProductForm.material.trim() || undefined,
        image: editProductForm.image.trim() || undefined
      };

      // Clear PNG cache for this product if image changed
      if (editingProduct.image !== updatedProduct.image) {
        clearProductImageCache();
      }

      // Check if this is a custom product or default product
      const isCustomProduct = customProducts.some(p => p.id === editingProduct.id);

      if (isCustomProduct) {
        // Update custom product
        updateCustomProduct(editingProduct.id, updatedProduct);
      } else {
        // For default products, we need to create a custom version
        // and mark the original as deleted/removed
        const newCustomProduct = {
          ...updatedProduct,
          id: `custom_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
          isCustom: true,
          originalId: editingProduct.id
        };

        // Add the custom product
        addCustomProduct(newCustomProduct);

        // Mark the original product as removed/deleted
        updateSetting('removedProducts', [...(settings.removedProducts || []), editingProduct.id]);
        updateSetting('deletedProducts', [...(settings.deletedProducts || []), editingProduct.id]);
      }

      // Trigger category changes callback
      if (onCategoryChanges) {
        onCategoryChanges();
      }

      // Close the edit modal
      setShowEditProductModal(false);
      setEditingProduct(null);

    } catch (error) {
      console.error('Error updating product:', error);
      setEditProductErrors({
        general: t('Failed to update product') || 'Failed to update product'
      });
    } finally {
      setIsUpdatingProduct(false);
    }
  };

  const handleCancelEditProduct = () => {
    setShowEditProductModal(false);
    setEditingProduct(null);
    setEditProductForm({
      name: '',
      category: '',
      subcategory: '',
      price: '',
      size: '',
      material: '',
      image: ''
    });
    setEditProductErrors({});
    setImagePreview('');
    setImageUploadError('');
  };

  // Image handling functions
  const validateImageFile = (file) => {
    const maxSize = 2 * 1024 * 1024; // 2MB
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];

    if (!allowedTypes.includes(file.type)) {
      return t('Unsupported image format. Please use PNG, JPG, JPEG, or WebP.') || 'Unsupported image format. Please use PNG, JPG, JPEG, or WebP.';
    }

    if (file.size > maxSize) {
      return t('Image size must be less than 2MB') || 'Image size must be less than 2MB';
    }

    return null;
  };

  const handleImageUpload = (event) => {
    const file = event.target.files[0];
    if (!file) return;

    const validationError = validateImageFile(file);
    if (validationError) {
      setImageUploadError(validationError);
      event.target.value = '';
      return;
    }

    setImageUploadError('');
    setIsProcessingImage(true);

    const reader = new FileReader();
    reader.onload = (e) => {
      const base64Data = e.target.result;
      setImagePreview(base64Data);
      setEditProductForm(prev => ({ ...prev, image: base64Data }));
      setIsProcessingImage(false);
    };

    reader.onerror = () => {
      setIsProcessingImage(false);
      setImageUploadError(t('Error processing image') || 'Error processing image');
    };

    reader.readAsDataURL(file);
    event.target.value = '';
  };

  const handleRemoveImage = () => {
    setImagePreview('');
    setEditProductForm(prev => ({ ...prev, image: '' }));
    setImageUploadError('');
  };

  const handleImageUrlChange = (url) => {
    setEditProductForm(prev => ({ ...prev, image: url }));
    setImagePreview(url);
    setImageUploadError('');
  };

  // Category editing functions
  const handleEditCategory = (category) => {
    setEditingCategory(category.name);
    setEditCategoryForm({
      name: category.name,
      description: category.description || ''
    });
    setEditCategoryErrors({});
  };

  const handleEditCategoryFormChange = (field, value) => {
    setEditCategoryForm(prev => ({
      ...prev,
      [field]: value
    }));

    // Clear error for this field when user starts typing
    if (editCategoryErrors[field]) {
      setEditCategoryErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateEditCategoryForm = () => {
    const errors = {};
    const allCategories = getAllCategories();

    if (!editCategoryForm.name.trim()) {
      errors.name = t('Category name is required') || 'Category name is required';
    } else {
      // Check for duplicate names (excluding the current category being edited)
      const isDuplicate = allCategories.some(cat =>
        cat.name.toLowerCase() === editCategoryForm.name.trim().toLowerCase() &&
        cat.name !== editingCategory
      );
      if (isDuplicate) {
        errors.name = t('Category name already exists') || 'Category name already exists';
      }
    }

    return errors;
  };

  const handleSaveEditedCategory = async () => {
    const errors = validateEditCategoryForm();

    if (Object.keys(errors).length > 0) {
      setEditCategoryErrors(errors);
      return;
    }

    try {
      const newName = editCategoryForm.name.trim();
      const oldName = editingCategory;

      // If name changed, we need to update all products in this category
      if (newName !== oldName) {
        // For now, we'll just update the custom categories
        // In a full implementation, you'd want to update all products too
        const customCategoryIndex = customCategories.findIndex(cat => cat.name === oldName);
        if (customCategoryIndex !== -1) {
          const updatedCategories = [...customCategories];
          updatedCategories[customCategoryIndex] = {
            ...updatedCategories[customCategoryIndex],
            name: newName,
            description: editCategoryForm.description
          };
          // Note: This would need to be implemented in the SettingsContext
          // For now, we'll just show a message
          console.log('Category update would happen here');
        }
      }

      // Reset editing state
      setEditingCategory(null);
      setEditCategoryForm({ name: '', description: '' });
      setEditCategoryErrors({});

      // Trigger category changes callback
      if (onCategoryChanges) {
        onCategoryChanges();
      }

    } catch (error) {
      console.error('Error updating category:', error);
      setEditCategoryErrors({
        general: t('Failed to update category') || 'Failed to update category'
      });
    }
  };

  const handleCancelEditCategory = () => {
    setEditingCategory(null);
    setEditCategoryForm({ name: '', description: '' });
    setEditCategoryErrors({});
  };

  // Handle the actual clear all data operation
  const handleClearAllData = async () => {
    setIsClearing(true);

    try {
      // Clear settings data first
      const settingsResult = clearAllData();

      // Clear data storage as well
      const storageResult = clearDataStorage();

      if (settingsResult.success && storageResult.success) {
        // Trigger category changes callback
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        // Show success message
        setDataOperationStatus({
          type: 'success',
          message: 'All application data cleared successfully'
        });

        // Close dialog after success
        setShowClearAllDialog(false);
      } else {
        const errorMessage = settingsResult.message || storageResult.message || 'Failed to clear data';
        console.error('Failed to clear data:', errorMessage);

        // Show error message
        setDataOperationStatus({
          type: 'error',
          message: 'Clear data failed: ' + errorMessage
        });

        // Close dialog
        setShowClearAllDialog(false);
      }
    } catch (error) {
      console.error('Error clearing all data:', error);

      // Show error message
      setDataOperationStatus({
        type: 'error',
        message: 'Clear data failed: ' + error.message
      });

      // Close dialog
      setShowClearAllDialog(false);
    } finally {
      setIsClearing(false);
    }
  };







  // Functions for deletion confirmation
  const handleDeleteCategory = (categoryName) => {
    const productCount = getCategoryProductCount(categoryName);
    setDeleteTarget({ name: categoryName, productCount });
    setDeleteType('category');
    setProductHandlingOption('orphan');
    setReassignTarget({ category: '', subcategory: '' });
    setDeleteError('');
    setShowDeleteConfirm(true);
  };

  const handleDeleteSubcategory = (categoryName, subcategoryName) => {
    const productCount = getSubcategoryProductCount(categoryName, subcategoryName);
    setDeleteTarget({
      name: subcategoryName,
      category: categoryName,
      productCount
    });
    setDeleteType('subcategory');
    setProductHandlingOption('orphan');
    setReassignTarget({ category: '', subcategory: '' });
    setDeleteError('');
    setShowDeleteConfirm(true);
  };

  const handleConfirmDeletion = async () => {
    setIsDeleting(true);
    setDeleteError('');

    try {
      let result;

      if (deleteType === 'category') {
        if (productHandlingOption === 'reassign' && reassignTarget.category) {
          // First reassign products, then delete category
          await reassignProducts(
            deleteTarget.name,
            null,
            reassignTarget.category,
            reassignTarget.subcategory || 'Other'
          );
        }

        result = permanentlyDeleteCategory(deleteTarget.name, productHandlingOption);
      } else if (deleteType === 'subcategory') {
        if (productHandlingOption === 'reassign' && reassignTarget.subcategory) {
          // First reassign products, then delete subcategory
          await reassignProducts(
            deleteTarget.category,
            deleteTarget.name,
            deleteTarget.category,
            reassignTarget.subcategory
          );
        }

        result = permanentlyDeleteSubcategory(
          deleteTarget.category,
          deleteTarget.name,
          productHandlingOption
        );
      }

      if (result && result.success) {
        setShowDeleteConfirm(false);
        // Force re-render by updating a state variable
        setExpandedCategories(prev => ({ ...prev }));
        // Force refresh of search term to trigger filteredProducts recalculation
        setSearchTerm(prev => prev);
        // Trigger category changes callback if provided
        if (onCategoryChanges) {
          onCategoryChanges();
        }
        // Show success message
        console.log(`Successfully deleted ${deleteType}: ${deleteTarget.name}`);
      } else {
        setDeleteError(result?.error || 'Deletion failed');
      }
    } catch (error) {
      console.error('Error during deletion:', error);
      setDeleteError(error.message || 'An unexpected error occurred');
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDeletion = () => {
    setShowDeleteConfirm(false);
    setDeleteTarget(null);
    setDeleteType('');
    setProductHandlingOption('orphan');
    setReassignTarget({ category: '', subcategory: '' });
    setDeleteError('');
    setIsDeleting(false);
  };






  // Function to handle Excel import
  const handleExcelImport = (event) => {
    const file = event.target.files[0];
    if (!file) {
      console.log('No file selected for import');
      return;
    }

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.xlsx')) {
      console.error('Invalid file type:', file.type);
      setImportStatus({
        type: 'error',
        message: `${t('excelImportError') || 'Error importing Excel file'}: Please select a .xlsx file`
      });
      setTimeout(() => setImportStatus(null), 5000);
      return;
    }

    console.log('=== EXCEL IMPORT STARTED ===');
    console.log('File name:', file.name);
    console.log('File size:', file.size, 'bytes');
    console.log('File type:', file.type);

    setImportStatus({ type: 'info', message: t('processingImages') || 'Processing Excel file...' });

    const reader = new FileReader();

    reader.onload = (e) => {
      try {
        console.log('=== FILE READ SUCCESSFUL ===');
        console.log('ArrayBuffer size:', e.target.result.byteLength);

        const data = new Uint8Array(e.target.result);
        console.log('Converting to Uint8Array, length:', data.length);

        const workbook = XLSX.read(data, { type: 'array' });
        console.log('=== EXCEL PARSING SUCCESSFUL ===');
        console.log('Available sheets:', workbook.SheetNames);

        // Get the Products worksheet with more flexible matching
        let productsSheetName = workbook.SheetNames.find(name => {
          const lowerName = name.toLowerCase();
          return lowerName.includes('product') ||
                 lowerName.includes('produit') ||
                 lowerName === 'products' ||
                 lowerName === 'produits';
        });

        // If no products sheet found, use the first sheet
        if (!productsSheetName) {
          productsSheetName = workbook.SheetNames[0];
          console.warn('No products sheet found, using first sheet:', productsSheetName);
        } else {
          console.log('Found products sheet:', productsSheetName);
        }

        if (!productsSheetName || workbook.SheetNames.length === 0) {
          console.error('No worksheets found in file');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: No worksheets found in file`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        console.log('=== PROCESSING WORKSHEET ===');
        console.log('Using sheet:', productsSheetName);

        const worksheet = workbook.Sheets[productsSheetName];
        if (!worksheet) {
          console.error('Worksheet not found:', productsSheetName);
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: Worksheet "${productsSheetName}" not found`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        const jsonData = XLSX.utils.sheet_to_json(worksheet);
        console.log('=== DATA PARSING SUCCESSFUL ===');
        console.log('Parsed data rows:', jsonData.length);
        console.log('Sample row (first):', jsonData[0]);
        console.log('Available columns:', jsonData.length > 0 ? Object.keys(jsonData[0]) : 'No data');

        if (jsonData.length === 0) {
          console.warn('No data rows found in worksheet');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: The sheet "${productsSheetName}" contains no data rows`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        // Validate that required functions are available
        if (typeof ensureCategoryExists !== 'function') {
          console.error('ensureCategoryExists function not available');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: Category management function not available`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        if (typeof addCustomProduct !== 'function') {
          console.error('addCustomProduct function not available');
          setImportStatus({
            type: 'error',
            message: `${t('excelImportError') || 'Error importing Excel file'}: Product creation function not available`
          });
          setTimeout(() => setImportStatus(null), 5000);
          return;
        }

        console.log('=== STARTING PRODUCT PROCESSING ===');
        let importedCount = 0;
        let errorCount = 0;
        const errors = [];
        let productsWithImages = 0;
        let productsWithoutImages = 0;

        jsonData.forEach((row, index) => {
          try {
            console.log(`=== PROCESSING ROW ${index + 2} ===`);
            console.log('Raw row data:', row);

            // Map column names with comprehensive support for different naming conventions
            const name = row['Name'] || row['Nom'] || row['name'] || row['nom'] ||
                        row['Product Name'] || row['Nom du Produit'] || '';
            const category = row['Category'] || row['Catégorie'] || row['category'] || row['catégorie'] || '';
            const subcategory = row['Subcategory'] || row['Sous-catégorie'] || row['subcategory'] ||
                              row['sous-catégorie'] || row['Sub-category'] || row['Sous catégorie'] || '';
            const priceRaw = row['Price'] || row['Prix'] || row['price'] || row['prix'] || 0;
            const size = row['Size'] || row['Taille'] || row['size'] || row['taille'] ||
                        row['Diameter'] || row['Diamètre'] || row['diameter'] || row['diamètre'] || '';
            const material = row['Material'] || row['Matériau'] || row['material'] || row['matériau'] ||
                           row['Materials'] || row['Matériaux'] || '';
            const image = row['Image (URL or base64)'] || row['Image (URL ou base64)'] ||
                         row['Image'] || row['image'] || row['Image URL'] || row['URL Image'] || '';

            console.log('Mapped values:', {
              name: name,
              category: category,
              subcategory: subcategory,
              priceRaw: priceRaw,
              size: size,
              material: material,
              imageLength: image ? image.length : 0
            });

            // Skip completely empty rows
            const hasAnyData = name || category || subcategory || priceRaw || size || material || image;
            if (!hasAnyData) {
              console.log(`Row ${index + 2}: Skipping completely empty row`);
              return;
            }

            // Validate required fields
            const missingFields = [];
            if (!name || (typeof name === 'string' && name.trim() === '')) {
              missingFields.push('name');
            }
            if (!category || (typeof category === 'string' && category.trim() === '')) {
              missingFields.push('category');
            }
            if (!subcategory || (typeof subcategory === 'string' && subcategory.trim() === '')) {
              missingFields.push('subcategory');
            }

            if (missingFields.length > 0) {
              const errorMsg = `Row ${index + 2}: Missing required fields: ${missingFields.join(', ')}`;
              console.warn(errorMsg);
              errors.push(errorMsg);
              errorCount++;
              return;
            }

            // Clean and validate data types
            const cleanName = name.toString().trim();
            const cleanCategory = category.toString().trim();
            const cleanSubcategory = subcategory.toString().trim();

            // Parse price with better error handling
            let cleanPrice = 0;
            if (priceRaw !== null && priceRaw !== undefined && priceRaw !== '') {
              const parsedPrice = parseFloat(priceRaw);
              if (!isNaN(parsedPrice) && parsedPrice >= 0) {
                cleanPrice = parsedPrice;
              } else {
                console.warn(`Row ${index + 2}: Invalid price value "${priceRaw}", using 0`);
              }
            }

            const cleanSize = size ? size.toString().trim() : '';
            const cleanMaterial = material ? material.toString().trim() : '';
            let cleanImage = image ? image.toString().trim() : '';

            console.log('Cleaned values:', {
              cleanName,
              cleanCategory,
              cleanSubcategory,
              cleanPrice,
              cleanSize,
              cleanMaterial,
              cleanImageLength: cleanImage.length
            });

            // Final validation of required fields after cleaning
            if (cleanName.length === 0 || cleanCategory.length === 0 || cleanSubcategory.length === 0) {
              const errorMsg = `Row ${index + 2}: Required fields cannot be empty after trimming`;
              console.warn(errorMsg);
              errors.push(errorMsg);
              errorCount++;
              return;
            }

            // Validate and process image data
            console.log(`Row ${index + 2}: Processing image data...`);
            if (cleanImage) {
              console.log(`Row ${index + 2}: Image data length: ${cleanImage.length}`);
              console.log(`Row ${index + 2}: Image data preview: ${cleanImage.substring(0, 100)}...`);

              // Check if it's a URL
              if (cleanImage.startsWith('http://') || cleanImage.startsWith('https://')) {
                try {
                  new URL(cleanImage);
                  console.log(`Row ${index + 2}: Valid image URL detected: ${cleanImage}`);
                } catch (urlError) {
                  console.warn(`Row ${index + 2}: Invalid image URL: ${cleanImage}`);
                  errors.push(`Row ${index + 2}: ${t('invalidImageUrl') || 'Invalid image URL'}: ${cleanImage.substring(0, 50)}...`);
                  cleanImage = ''; // Clear invalid URL but continue with import
                }
              }
              // Check if it's base64 data
              else if (cleanImage.startsWith('data:image/')) {
                const base64Pattern = /^data:image\/(jpeg|jpg|png|gif|svg\+xml);base64,([A-Za-z0-9+/=]+)$/;
                if (!base64Pattern.test(cleanImage)) {
                  console.warn(`Row ${index + 2}: Invalid base64 image data format`);
                  errors.push(`Row ${index + 2}: ${t('invalidImageData') || 'Invalid image data'} - must be valid base64 format`);
                  cleanImage = ''; // Clear invalid base64 but continue with import
                } else {
                  console.log(`Row ${index + 2}: Valid base64 image detected (${cleanImage.length} chars)`);
                }
              }
              // If it's not empty but doesn't match expected formats
              else if (cleanImage.length > 0) {
                console.warn(`Row ${index + 2}: Image data doesn't match expected format (URL or base64): ${cleanImage.substring(0, 50)}...`);
                errors.push(`Row ${index + 2}: ${t('invalidImageData') || 'Invalid image data'} - must be URL or base64 format`);
                cleanImage = ''; // Clear invalid data but continue with import
              }
            } else {
              console.log(`Row ${index + 2}: No image data provided`);
            }

            // Ensure category and subcategory exist
            console.log(`Row ${index + 2}: Creating category "${cleanCategory}" with subcategory "${cleanSubcategory}"`);
            try {
              ensureCategoryExists(cleanCategory, cleanSubcategory);
              console.log(`Row ${index + 2}: Category creation successful`);
            } catch (categoryError) {
              console.error(`Row ${index + 2}: Category creation failed:`, categoryError);
              const errorMsg = `Row ${index + 2}: Failed to create category "${cleanCategory}" > "${cleanSubcategory}": ${categoryError.message}`;
              errors.push(errorMsg);
              errorCount++;
              return;
            }

            // Create product object with unique ID
            const product = {
              id: `imported-${Date.now()}-${index}-${Math.random().toString(36).substr(2, 9)}`,
              name: cleanName,
              category: cleanCategory,
              subcategory: cleanSubcategory,
              price: cleanPrice,
              diameter: cleanSize, // For backward compatibility
              size: cleanSize,
              material: cleanMaterial,
              image: cleanImage,
              isCustom: true,
              createdAt: new Date().toISOString()
            };

            console.log(`Row ${index + 2}: Created product object:`, product);

            // Track image statistics
            if (cleanImage) {
              productsWithImages++;
              console.log(`Row ${index + 2}: Product has image (${cleanImage.length} chars)`);
            } else {
              productsWithoutImages++;
              console.log(`Row ${index + 2}: Product has no image`);
            }

            // Add the product
            console.log(`Row ${index + 2}: Adding product to system...`);
            try {
              const addedProduct = addCustomProduct(product);
              importedCount++;
              console.log(`Row ${index + 2}: ✅ Successfully imported product: "${product.name}" (${product.category} > ${product.subcategory})${cleanImage ? ' with image' : ''}`);
              console.log(`Row ${index + 2}: Added product ID: ${addedProduct?.id || 'unknown'}`);
            } catch (productError) {
              console.error(`Row ${index + 2}: Product creation failed:`, productError);
              const errorMsg = `Row ${index + 2}: Failed to create product "${cleanName}": ${productError.message}`;
              errors.push(errorMsg);
              errorCount++;
            }

          } catch (error) {
            const errorMsg = `Row ${index + 2}: Unexpected error: ${error.message}`;
            console.error(`Row ${index + 2}: ❌ Unexpected error:`, error);
            errors.push(errorMsg);
            errorCount++;
          }
        });

        console.log('=== IMPORT COMPLETED ===');
        console.log(`Total processed: ${jsonData.length} rows`);
        console.log(`Successfully imported: ${importedCount} products`);
        console.log(`Errors encountered: ${errorCount}`);
        console.log(`Products with images: ${productsWithImages}`);
        console.log(`Products without images: ${productsWithoutImages}`);
        console.log('Error details:', errors);

        // Show comprehensive import results
        if (importedCount > 0) {
          const productsText = importedCount === 1 ? 'product' : 'products';
          let successMessage = `${t('excelImportSuccess') || 'Products imported successfully'}: ${importedCount} ${productsText}`;

          // Add detailed image statistics
          if (productsWithImages > 0 || productsWithoutImages > 0) {
            const imageStats = [];
            if (productsWithImages > 0) {
              imageStats.push(`${productsWithImages} ${t('importedWithImages') || 'with images'}`);
            }
            if (productsWithoutImages > 0) {
              imageStats.push(`${productsWithoutImages} ${t('importedWithoutImages') || 'without images'}`);
            }
            successMessage += ` (${imageStats.join(', ')})`;
          }

          // Add error summary if there are errors
          if (errorCount > 0) {
            successMessage += `. ${errorCount} row${errorCount === 1 ? '' : 's'} had errors and ${errorCount === 1 ? 'was' : 'were'} skipped.`;
          }

          setImportStatus({
            type: 'success',
            message: successMessage
          });

          // Log detailed success info
          console.log('✅ IMPORT SUCCESS:', successMessage);

        } else {
          // No products imported - show detailed error information
          let errorMessage;
          if (errors.length > 0) {
            errorMessage = `${t('excelImportError') || 'Error importing Excel file'}: ${errors[0]}`;
            if (errors.length > 1) {
              errorMessage += ` (and ${errors.length - 1} other error${errors.length > 2 ? 's' : ''})`;
            }
          } else {
            errorMessage = `${t('excelImportError') || 'Error importing Excel file'}: No valid products found in the file`;
          }

          setImportStatus({
            type: 'error',
            message: errorMessage
          });

          // Log detailed error info
          console.log('❌ IMPORT FAILED:', errorMessage);
          console.log('All errors:', errors);
        }

        // Show status for longer time if there are errors to review
        const statusTimeout = errorCount > 0 ? 8000 : 5000;
        setTimeout(() => setImportStatus(null), statusTimeout);

      } catch (error) {
        console.error('=== CRITICAL IMPORT ERROR ===');
        console.error('Error type:', error.constructor.name);
        console.error('Error message:', error.message);
        console.error('Error stack:', error.stack);

        let errorMessage = `${t('excelImportError') || 'Error importing Excel file'}: `;

        // Provide specific error messages based on error type
        if (error.message.includes('Invalid file format')) {
          errorMessage += 'Invalid Excel file format. Please ensure the file is a valid .xlsx file.';
        } else if (error.message.includes('Cannot read property')) {
          errorMessage += 'File structure error. Please check that the Excel file has the correct format.';
        } else if (error.message.includes('XLSX')) {
          errorMessage += 'Excel parsing error. The file may be corrupted or in an unsupported format.';
        } else {
          errorMessage += error.message;
        }

        setImportStatus({
          type: 'error',
          message: errorMessage
        });
        setTimeout(() => setImportStatus(null), 8000);
      }
    };

    reader.onerror = (error) => {
      console.error('=== FILE READER ERROR ===');
      console.error('FileReader error:', error);
      console.error('File details:', {
        name: file.name,
        size: file.size,
        type: file.type,
        lastModified: file.lastModified
      });

      setImportStatus({
        type: 'error',
        message: `${t('excelImportError') || 'Error importing Excel file'}: Failed to read file. The file may be corrupted or too large.`
      });
      setTimeout(() => setImportStatus(null), 8000);
    };

    console.log('=== STARTING FILE READ ===');
    reader.readAsArrayBuffer(file);

    // Reset the input value so the same file can be imported again
    event.target.value = '';
  };







  // Render the modal content based on active tab
  const renderTabContent = () => {
    switch (activeTab) {
      case 'categories':
        return renderCategoriesTab();
      case 'products':
        return renderProductsTab();
      case 'settings':
        return renderSettingsTab();
      case 'database':
        return renderDatabaseTab();
      default:
        return renderCategoriesTab();
    }
  };

  // Render Database Tab (Consolidated with Data Storage functionality)
  const renderDatabaseTab = () => {
    return (
      <div className="p-4 flex flex-col h-full">
        {/* Status Messages */}
        {dataOperationStatus && (
          <div className={`mb-4 p-3 rounded ${
            dataOperationStatus.type === 'success'
              ? 'bg-green-50 border border-green-200 text-green-800'
              : dataOperationStatus.type === 'info'
              ? 'bg-blue-50 border border-blue-200 text-blue-800'
              : 'bg-red-50 border border-red-200 text-red-800'
          }`}>
            {dataOperationStatus.message}
          </div>
        )}

        {/* Tabs for database sections */}
        <div className="flex border-b mb-4">
          {[
            { id: 'overview', label: 'Overview', icon: Database },
            { id: 'backups', label: 'Backups', icon: Clock },
            { id: 'settings', label: 'Settings', icon: Settings }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => setDataStorageTab(tab.id)}
              className={`flex items-center space-x-2 px-4 py-2 text-sm font-medium transition-colors ${
                dataStorageTab === tab.id
                  ? 'text-blue-600 border-b-2 border-blue-600'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              <tab.icon className="w-4 h-4" />
              <span>{tab.label}</span>
            </button>
          ))}
        </div>

        {/* Tab content */}
        <div className="flex-1 overflow-y-auto">
          {dataStorageTab === 'overview' && (
            <div className="space-y-6">
              {/* Database Status */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                    <Database className="w-4 h-4 mr-2" />
                    Database Status
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>SQLite Enabled:</span>
                      <span className={sqliteEnabled ? 'text-green-600' : 'text-red-600'}>
                        {sqliteEnabled ? 'Yes' : 'No'}
                      </span>
                    </div>
                    <div className="flex justify-between">
                      <span>Migration Status:</span>
                      <span className={migrationStatus === 'completed' ? 'text-green-600' : 'text-yellow-600'}>
                        {migrationStatus === 'completed' ? 'Complete' : 'Pending'}
                      </span>
                    </div>
                    {databaseStats && (
                      <>
                        <div className="flex justify-between">
                          <span>Total Products:</span>
                          <span className="font-medium">{databaseStats.sqlite?.totalProducts || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Database Size:</span>
                          <span className="font-medium">{databaseStats.sqlite?.formattedSize || 'Unknown'}</span>
                        </div>
                      </>
                    )}
                  </div>
                </div>

                <div className="bg-gray-50 p-4 rounded-lg">
                  <h4 className="font-medium text-gray-900 mb-2 flex items-center">
                    <HardDrive className="w-4 h-4 mr-2" />
                    Storage Information
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span>Total Size:</span>
                      <span className="font-medium">{storageInfo?.formattedSize || '0 Bytes'}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Items:</span>
                      <span className="font-medium">{storageInfo?.itemCount || 0}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Last Save:</span>
                      <span className="font-medium">{formatDate(lastSaveTime)}</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Auto-save:</span>
                      <span className={`font-medium ${autoSaveEnabled ? 'text-green-600' : 'text-red-600'}`}>
                        {autoSaveEnabled ? 'Enabled' : 'Disabled'}
                      </span>
                    </div>
                  </div>
                </div>
              </div>

              {/* Enhanced Data Summary */}
              <div className="bg-gray-50 rounded-lg p-4">
                <h4 className="font-medium text-gray-900 mb-3">Data Summary</h4>
                <div className="grid grid-cols-2 md:grid-cols-3 gap-4 text-sm">
                  <div>
                    <span className="text-gray-600">Categories:</span>
                    <span className="text-gray-900 ml-2 font-medium">
                      {getAllCategories().length} ({customCategories.length} custom)
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Products:</span>
                    <span className="text-gray-900 ml-2 font-medium">
                      {allProducts.filter(p => !(settings.deletedProducts || []).includes(p.id)).length} ({customProducts.length} custom)
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Canvas Items:</span>
                    <span className="text-gray-900 ml-2 font-medium">{canvasProducts?.length || 0}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Connections:</span>
                    <span className="text-gray-900 ml-2 font-medium">{connections?.length || 0}</span>
                  </div>
                  <div>
                    <span className="text-gray-600">Products with Images:</span>
                    <span className="text-gray-900 ml-2 font-medium">
                      {allProducts.filter(p => p.image && p.image.length > 0 && !(settings.deletedProducts || []).includes(p.id)).length}
                    </span>
                  </div>
                  <div>
                    <span className="text-gray-600">Products with Materials:</span>
                    <span className="text-gray-900 ml-2 font-medium">
                      {allProducts.filter(p => p.material && p.material.length > 0 && !(settings.deletedProducts || []).includes(p.id)).length}
                    </span>
                  </div>
                </div>
              </div>

              {/* Quick Actions */}
              <div className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={handleDataExport}
                    disabled={isDataLoading}
                    className="flex items-center justify-center space-x-2 p-4 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded-lg transition-colors"
                  >
                    {isDataLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span>Exporting...</span>
                      </>
                    ) : (
                      <>
                        <Download className="w-5 h-5" />
                        <span>Export Data</span>
                      </>
                    )}
                  </button>

                  <label className={`flex items-center justify-center space-x-2 p-4 rounded-lg transition-colors cursor-pointer ${
                    isDataLoading
                      ? 'bg-gray-400 cursor-not-allowed'
                      : 'bg-green-600 hover:bg-green-700'
                  } text-white`}>
                    {isDataLoading ? (
                      <>
                        <div className="w-5 h-5 border-2 border-white border-t-transparent rounded-full animate-spin" />
                        <span>Importing...</span>
                      </>
                    ) : (
                      <>
                        <Upload className="w-5 h-5" />
                        <span>Import Data</span>
                      </>
                    )}
                    <input
                      type="file"
                      accept=".json"
                      onChange={handleDataImport}
                      className="hidden"
                      disabled={isDataLoading}
                    />
                  </label>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <button
                    onClick={() => setShowDatabasePanel(true)}
                    className="flex items-center justify-center space-x-2 p-4 bg-purple-600 hover:bg-purple-700 text-white rounded-lg transition-colors"
                  >
                    <Database className="w-5 h-5" />
                    <span>Advanced Database Manager</span>
                  </button>
                  <button
                    onClick={refreshDatabaseStats}
                    className="flex items-center justify-center space-x-2 p-4 bg-gray-600 hover:bg-gray-700 text-white rounded-lg transition-colors"
                  >
                    <RefreshCw className="w-5 h-5" />
                    <span>Refresh Statistics</span>
                  </button>
                </div>

                {/* Large file support note */}
                <div className="bg-blue-50 border border-blue-200 rounded p-3">
                  <p className="text-sm text-blue-800">
                    <strong>Large File Support:</strong> Files of any size are supported. Large files (100MB+) may take longer to import.
                  </p>
                </div>
              </div>

              {/* Features Overview */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="font-medium text-blue-900 mb-2">Enhanced Database Features</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• SQLite database for gigabyte-scale product storage</li>
                  <li>• Automatic duplicate detection and handling</li>
                  <li>• External image file storage to optimize database size</li>
                  <li>• Advanced search and filtering capabilities</li>
                  <li>• Bulk import/export operations with progress tracking</li>
                  <li>• Automatic migration from localStorage</li>
                </ul>
              </div>

              {/* Recent Products */}
              {databaseStats?.sqlite?.recentProducts && databaseStats.sqlite.recentProducts.length > 0 && (
                <div className="bg-white border border-gray-200 rounded-lg p-4">
                  <h4 className="font-medium text-gray-900 mb-3">Recent Products</h4>
                  <div className="space-y-2 max-h-40 overflow-y-auto">
                    {databaseStats.sqlite.recentProducts.map((product, index) => (
                      <div key={index} className="flex justify-between items-center text-sm">
                        <div>
                          <span className="font-medium">{product.name}</span>
                          <span className="text-gray-500 ml-2">({product.category})</span>
                        </div>
                        <span className="text-xs text-gray-400">
                          {new Date(product.lastUpdated).toLocaleDateString()}
                        </span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Danger Zone */}
              <div className="bg-red-50 border border-red-200 rounded-lg p-4">
                <div className="flex items-center space-x-2 mb-3">
                  <AlertTriangle className="w-5 h-5 text-red-600" />
                  <h3 className="font-medium text-red-600">Danger Zone</h3>
                </div>
                <p className="text-sm text-gray-700 mb-3">
                  Clear all stored data including canvas layouts, connections, and settings.
                </p>
                <button
                  onClick={confirmClearAllData}
                  disabled={isDataLoading}
                  className="flex items-center space-x-2 px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded text-sm transition-colors"
                >
                  <Trash2 className="w-4 h-4" />
                  <span>Clear All Data</span>
                </button>
              </div>
            </div>
          )}

          {dataStorageTab === 'backups' && (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-gray-900">Available Backups</h3>
                <div className="flex items-center space-x-2">
                  <button
                    onClick={() => setBackups(getBackups())}
                    disabled={isDataLoading || isDeletingAllBackups}
                    className="flex items-center space-x-2 px-3 py-1 bg-gray-100 hover:bg-gray-200 disabled:opacity-50 text-gray-700 rounded text-sm transition-colors"
                  >
                    <RefreshCw className="w-4 h-4" />
                    <span>Refresh</span>
                  </button>
                  {backups.length > 0 && (
                    <button
                      onClick={confirmDeleteAllBackups}
                      disabled={isDataLoading || isDeletingAllBackups}
                      className="flex items-center space-x-2 px-3 py-1 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded text-sm transition-colors"
                    >
                      <Trash2 className="w-4 h-4" />
                      <span>Delete All ({backups.length})</span>
                    </button>
                  )}
                </div>
              </div>

              {isDeletingAllBackups ? (
                <div className="text-center py-8 text-gray-500">
                  <div className="w-8 h-8 border-2 border-gray-300 border-t-blue-600 rounded-full animate-spin mx-auto mb-3" />
                  <p>Deleting all backups...</p>
                </div>
              ) : backups.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <Database className="w-12 h-12 mx-auto mb-3 opacity-50" />
                  <p>No backups available</p>
                  <p className="text-xs mt-2 text-gray-400">Backups are created automatically when you save your work</p>
                </div>
              ) : (
                <div className="space-y-2">
                  {backups.map((backup) => (
                    <div
                      key={backup.key}
                      className="flex items-center justify-between p-3 bg-gray-50 rounded-lg border border-gray-200"
                    >
                      <div className="flex-1">
                        <div className="text-gray-900 text-sm font-medium">
                          {formatDate(backup.timestamp)}
                        </div>
                        <div className="text-gray-500 text-xs">
                          Size: {Math.round(backup.size / 1024)} KB
                          {backup.version && (
                            <span className="ml-2">• Version: {backup.version}</span>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button
                          onClick={() => handleRestoreBackup(backup.key)}
                          disabled={isDataLoading || isDeletingAllBackups}
                          className="px-3 py-1 bg-blue-600 hover:bg-blue-700 disabled:opacity-50 text-white rounded text-sm transition-colors flex items-center space-x-1"
                        >
                          {isDataLoading ? (
                            <div className="w-3 h-3 border border-white border-t-transparent rounded-full animate-spin" />
                          ) : (
                            <RefreshCw className="w-3 h-3" />
                          )}
                          <span>Restore</span>
                        </button>
                        <button
                          onClick={() => confirmDeleteBackup(backup)}
                          disabled={isDataLoading || isDeletingAllBackups}
                          className="px-3 py-1 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded text-sm transition-colors flex items-center space-x-1"
                        >
                          <Trash2 className="w-3 h-3" />
                          <span>Delete</span>
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </div>
          )}

          {dataStorageTab === 'settings' && (
            <div className="space-y-6">
              <div className="space-y-4">
                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Auto-save</h4>
                    <p className="text-sm text-gray-600">Automatically save your work every 5 minutes</p>
                  </div>
                  <button
                    onClick={toggleAutoSave}
                    className={`relative inline-flex h-6 w-11 items-center rounded-full transition-colors ${
                      autoSaveEnabled ? 'bg-blue-600' : 'bg-gray-300'
                    }`}
                  >
                    <span
                      className={`inline-block h-4 w-4 transform rounded-full bg-white transition-transform ${
                        autoSaveEnabled ? 'translate-x-6' : 'translate-x-1'
                      }`}
                    />
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Auto-load</h4>
                    <p className="text-sm text-gray-600">Automatically restore your work when the app starts</p>
                  </div>
                  <button
                    className="relative inline-flex h-6 w-11 items-center rounded-full bg-blue-600"
                  >
                    <span className="inline-block h-4 w-4 transform rounded-full bg-white translate-x-6" />
                  </button>
                </div>

                <div className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                  <div>
                    <h4 className="font-medium text-gray-900">Enhanced Database</h4>
                    <p className="text-sm text-gray-600">Use SQLite for improved performance and scalability</p>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className={`text-sm font-medium ${sqliteEnabled ? 'text-green-600' : 'text-red-600'}`}>
                      {sqliteEnabled ? 'Enabled' : 'Disabled'}
                    </span>
                    {!sqliteEnabled && (
                      <button
                        onClick={initializeEnhancedStorage}
                        className="px-3 py-1 bg-blue-600 hover:bg-blue-700 text-white rounded text-sm transition-colors"
                      >
                        Enable
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    );
  };

  // Render Categories Tab
  const renderCategoriesTab = () => {
    const allCategories = getAllCategories();

    return (
      <div className="p-4 flex flex-col h-full">
        <div className="mb-4 flex items-center">
          <input
            type="text"
            placeholder={t('Search categories...')}
            className="w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
          <button
            onClick={() => resetCategoryVisibility()}
            className="ml-2 p-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors"
            title={t('Reset visibility')}
          >
            <RotateCcw size={16} />
          </button>
        </div>

        <div className="overflow-y-auto flex-1">
          {allCategories.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 text-lg">{t('No categories available')}</p>
              <p className="text-gray-500 text-sm mt-2">{t('Add products to create categories automatically')}</p>
            </div>
          ) : (
            allCategories
              .filter(cat => !searchTerm || cat.name.toLowerCase().includes(searchTerm.toLowerCase()))
              .map((category) => {
                const isVisible = settings.visibleCategories[category.name] || false;
                const isRemoved = settings.removedCategories.includes(category.name);
                const isExpanded = expandedCategories[category.name] || false;

                return (
                  <div key={category.name} className={`mb-2 border border-gray-200 rounded ${isRemoved ? 'bg-gray-50' : 'bg-white'}`}>
                    <div className="p-3 flex items-center justify-between">
                      <div className="flex items-center flex-1">
                        <button
                          onClick={() => toggleCategoryExpanded(category.name)}
                          className="mr-2 p-1 rounded hover:bg-gray-100 text-gray-700"
                        >
                          {isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />}
                        </button>

                        {/* Category name - editable when in edit mode */}
                        {editingCategory === category.name ? (
                          <div className="flex items-center flex-1 mr-2">
                            <input
                              type="text"
                              value={editCategoryForm.name}
                              onChange={(e) => handleEditCategoryFormChange('name', e.target.value)}
                              className={`flex-1 p-2 border rounded text-sm text-gray-900 bg-white ${
                                editCategoryErrors.name
                                  ? 'border-red-300 focus:border-red-500 focus:ring-red-200'
                                  : 'border-gray-300 focus:border-blue-500 focus:ring-blue-200'
                              } focus:outline-none focus:ring-2 placeholder-gray-500`}
                              placeholder={t('Category name')}
                              autoFocus
                            />
                            <div className="flex items-center ml-2">
                              <button
                                onClick={handleSaveEditedCategory}
                                className="p-1 text-green-600 rounded hover:bg-green-100 bg-green-50 transition-colors mr-1"
                                title={t('Save changes')}
                              >
                                <CheckCircle size={16} />
                              </button>
                              <button
                                onClick={handleCancelEditCategory}
                                className="p-1 text-gray-600 rounded hover:bg-gray-100 bg-gray-50 transition-colors"
                                title={t('Cancel')}
                              >
                                <X size={16} />
                              </button>
                            </div>
                          </div>
                        ) : (
                          <>
                            <span className={`font-medium ${isRemoved ? 'text-gray-500 line-through' : 'text-gray-900'}`}>
                              {category.name}
                            </span>
                            {category.isCustom && <span className="ml-2 text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded">Custom</span>}
                          </>
                        )}

                        {/* Error message for category editing */}
                        {editingCategory === category.name && editCategoryErrors.name && (
                          <div className="ml-2 text-xs text-red-600">
                            {editCategoryErrors.name}
                          </div>
                        )}
                      </div>

                      <div className="flex items-center">
                        {!isRemoved && editingCategory !== category.name ? (
                          <>
                            <button
                              onClick={() => handleEditCategory(category)}
                              className="mr-2 p-2 text-blue-600 rounded hover:bg-blue-100 bg-blue-50 transition-colors"
                              title={t('Edit category')}
                            >
                              <Edit size={16} />
                            </button>
                            <button
                              onClick={() => toggleCategoryVisibility(category.name)}
                              className={`mr-2 p-2 rounded transition-colors ${isVisible ? 'text-green-600 hover:bg-green-100 bg-green-50' : 'text-red-600 hover:bg-red-100 bg-red-50'}`}
                              title={isVisible ? t('Hide category') : t('Show category')}
                            >
                              {isVisible ? <Eye size={16} /> : <EyeOff size={16} />}
                            </button>
                            <button
                              onClick={() => removeCategory(category.name)}
                              className="p-2 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                              title={t('Remove category')}
                            >
                              <Trash2 size={16} />
                            </button>
                          </>
                        ) : !isRemoved && editingCategory === category.name ? (
                          // Hide other buttons when editing
                          null
                        ) : (
                          <>
                            <button
                              onClick={() => restoreCategory(category.name)}
                              className="mr-2 p-2 text-green-600 rounded hover:bg-green-100 bg-green-50 transition-colors"
                              title={t('Restore category')}
                            >
                              <RotateCcw size={16} />
                            </button>
                            <button
                              onClick={() => handleDeleteCategory(category.name)}
                              className="p-2 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                              title={t('Delete permanently')}
                            >
                              <Trash2 size={16} />
                            </button>
                          </>
                        )}
                      </div>
                    </div>

                    {isExpanded && (
                      <div className="pl-8 pr-3 pb-3 border-t border-gray-100">
                        {category.subcategories.map((subcategory) => {
                          const isSubcategoryRemoved = settings.removedSubcategories.some(
                            item => item.category === category.name && item.subcategory === subcategory
                          );

                          return (
                            <div key={subcategory} className="flex items-center justify-between py-2 border-b border-gray-50 last:border-b-0">
                              <span className={`text-sm ${isSubcategoryRemoved ? 'text-gray-500 line-through' : 'text-gray-700'}`}>
                                {subcategory}
                              </span>
                              {!isSubcategoryRemoved ? (
                                <button
                                  onClick={() => removeSubcategory(category.name, subcategory)}
                                  className="p-1 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                                  title={t('Remove subcategory')}
                                >
                                  <Trash2 size={14} />
                                </button>
                              ) : (
                                <div className="flex">
                                  <button
                                    onClick={() => restoreSubcategory(category.name, subcategory)}
                                    className="mr-1 p-1 text-green-600 rounded hover:bg-green-100 bg-green-50 transition-colors"
                                    title={t('Restore subcategory')}
                                  >
                                    <RotateCcw size={14} />
                                  </button>
                                  <button
                                    onClick={() => handleDeleteSubcategory(category.name, subcategory)}
                                    className="p-1 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                                    title={t('Delete permanently')}
                                  >
                                    <Trash2 size={14} />
                                  </button>
                                </div>
                              )}
                            </div>
                          );
                        })}
                      </div>
                    )}
                  </div>
                );
              })
          )}
        </div>
      </div>
    );
  };

  // Render Products Tab
  const renderProductsTab = () => {
    return (
      <div className="p-4 flex flex-col h-full">
        <div className="mb-4">
          <input
            type="text"
            placeholder={t('Search products...')}
            className="w-full p-2 border border-gray-300 rounded bg-white text-gray-900 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
          />
        </div>

        <div className="overflow-y-auto flex-1">
          {filteredProducts.length === 0 ? (
            <div className="text-center py-8">
              <p className="text-gray-600 text-lg">{t('No products available')}</p>
              <p className="text-gray-500 text-sm mt-2">{t('Use the "Add Product" button to create your first product')}</p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
              {filteredProducts.map((product) => {
                return (
                  <div
                    key={product.id}
                    className="border border-gray-200 rounded-lg p-4 bg-white shadow-sm hover:shadow-md transition-shadow"
                  >
                    <div className="flex justify-between items-start mb-3">
                      <div>
                        <h3 className="font-bold text-sm text-gray-900">{product.name}</h3>
                        <p className="text-xs text-gray-600">{product.category} &gt; {product.subcategory}</p>
                      </div>
                      <div className="flex space-x-1">
                        <button
                          onClick={() => handleEditProduct(product)}
                          className="p-2 text-blue-600 rounded hover:bg-blue-100 bg-blue-50 transition-colors"
                          title={t('Edit Product')}
                        >
                          <Edit size={16} />
                        </button>
                        <button
                          onClick={() => permanentlyDeleteProduct(product.id)}
                          className="p-2 text-red-600 rounded hover:bg-red-100 bg-red-50 transition-colors"
                          title={t('Delete product')}
                        >
                          <Trash2 size={16} />
                        </button>
                      </div>
                    </div>

                    <div className="flex items-center">
                      <div className="w-12 h-12 bg-gray-100 border border-gray-200 rounded mr-3 flex-shrink-0 flex items-center justify-center overflow-hidden">
                        {product.image ? (
                          <img src={product.image} alt={product.name} className="object-contain max-w-full max-h-full" />
                        ) : (
                          <Package size={20} className="text-gray-400" />
                        )}
                      </div>
                      <div className="flex-1">
                        <p className="text-xs font-medium text-gray-700">
                          {t('Price')}: <span className="text-green-600 font-bold">${product.price?.toFixed(2) || '0.00'}</span>
                        </p>
                        <p className="text-xs text-gray-600">
                          {t('Size')}: {product.diameter || product.size || 'N/A'}
                        </p>
                        {product.material && (
                          <p className="text-xs text-gray-600">
                            {t('Material')}: {product.material}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
    );
  };



  // Data storage management functions
  const handleDataExport = async () => {
    setIsDataLoading(true);
    try {
      // Get comprehensive application data
      const dataSummary = getDataSummary();
      const allCategories = getAllCategories();

      // Get all products with complete details (default + custom)
      const allProducts = [...defaultProducts, ...customProducts];

      // Filter out deleted products and include all product details
      const exportableProducts = allProducts
        .filter(product => !(settings.deletedProducts || []).includes(product.id))
        .map(product => ({
          // Core product information
          id: product.id,
          name: product.name,
          category: product.category,
          subcategory: product.subcategory,

          // Product details with complete data preservation
          price: product.price,
          size: product.size || product.diameter || '',
          diameter: product.diameter || product.size || '',
          material: product.material || '',
          image: product.image || '',

          // Product metadata
          isCustom: product.isCustom || false,
          createdAt: product.createdAt || '',
          updatedAt: product.updatedAt || '',

          // Additional fields that might exist
          description: product.description || '',
          brand: product.brand || '',
          model: product.model || '',
          weight: product.weight || '',
          color: product.color || '',

          // Preserve any other custom fields
          ...Object.keys(product).reduce((acc, key) => {
            if (!['id', 'name', 'category', 'subcategory', 'price', 'size', 'diameter', 'material', 'image', 'isCustom', 'createdAt', 'updatedAt', 'description', 'brand', 'model', 'weight', 'color'].includes(key)) {
              acc[key] = product[key];
            }
            return acc;
          }, {})
        }));

      const dataToExport = {
        // Canvas data with complete product information
        canvasProducts,
        connections,
        selectedProducts: [],

        // Complete application data
        settings: settings,
        categories: allCategories,
        products: exportableProducts,
        customProducts: customProducts,
        customCategories: customCategories,

        // Storage data
        storageInfo: storageInfo,

        // Enhanced metadata with detailed counts
        metadata: {
          version: '1.0.0',
          exported: new Date().toISOString(),
          exportedBy: 'PlomDesign',

          // Detailed product counts
          totalProducts: exportableProducts.length,
          customProductsCount: customProducts.length,
          defaultProductsCount: defaultProducts.filter(p => !(settings.deletedProducts || []).includes(p.id)).length,

          // Category information
          totalCategories: allCategories.length,
          customCategoriesCount: customCategories.length,
          defaultCategoriesCount: productCategories.filter(cat => !(settings.deletedCategories || []).includes(cat.name)).length,

          // Canvas information
          canvasProductCount: canvasProducts?.length || 0,
          connectionCount: connections?.length || 0,

          // Data integrity information
          productsWithImages: exportableProducts.filter(p => p.image && p.image.length > 0).length,
          productsWithSizes: exportableProducts.filter(p => p.size && p.size.length > 0).length,
          productsWithMaterials: exportableProducts.filter(p => p.material && p.material.length > 0).length,

          // Export statistics
          exportSize: JSON.stringify(exportableProducts).length,
          imageDataSize: exportableProducts.reduce((total, p) => total + (p.image ? p.image.length : 0), 0)
        }
      };

      const result = exportData(dataToExport);
      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: `Data exported successfully (${exportableProducts.length} products, ${allCategories.length} categories, ${dataToExport.metadata.productsWithImages} with images)`
        });
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      console.error('Export error:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Export failed: ' + error.message
      });
    } finally {
      setIsDataLoading(false);
    }
  };

  const handleDataImport = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    // Validate file type
    if (!file.name.toLowerCase().endsWith('.json')) {
      setDataOperationStatus({
        type: 'error',
        message: 'Invalid file type. Please select a JSON file.'
      });
      event.target.value = '';
      return;
    }

    // Show file size info for user awareness
    const fileSizeMB = Math.round(file.size / (1024 * 1024));
    console.log(`User selected file: ${file.name} (${fileSizeMB}MB)`);

    // Show warning for very large files but don't block
    if (file.size > 100 * 1024 * 1024) {
      setDataOperationStatus({
        type: 'info',
        message: `Large file detected (${fileSizeMB}MB). Import may take some time...`
      });
    }

    setIsDataLoading(true);
    try {
      const result = await importData(file);
      if (result.success && result.data) {
        // Update application state with imported data
        const importedData = result.data;

        // Validate imported data structure
        if (!importedData || typeof importedData !== 'object') {
          throw new Error('Invalid data format in imported file');
        }

        console.log('Imported data structure:', Object.keys(importedData));
        console.log('Canvas products:', importedData.canvasProducts?.length || 0);
        console.log('Connections:', importedData.connections?.length || 0);

        // Update canvas data if available and we have the update function
        if (importedData.canvasProducts && Array.isArray(importedData.canvasProducts) && onLoadProject) {
          console.log('Updating canvas products:', importedData.canvasProducts.length);

          // Use the onLoadProject callback to update the canvas state
          onLoadProject({
            products: importedData.canvasProducts,
            connections: importedData.connections || [],
            selectedProducts: importedData.selectedProducts || []
          });

          // Also save the imported data to localStorage for persistence
          try {
            const saveResult = await saveApplicationData(importedData);
            if (saveResult.success) {
              console.log('Imported data saved to localStorage successfully');
            } else {
              console.warn('Failed to save imported data to localStorage:', saveResult.message);
            }
          } catch (saveError) {
            console.warn('Error saving imported data to localStorage:', saveError);
          }
        }

        // Update settings if available
        if (importedData.settings && typeof importedData.settings === 'object') {
          try {
            updateSettings(importedData.settings);
            console.log('Settings updated successfully');
          } catch (settingsError) {
            console.warn('Failed to import settings:', settingsError);
          }
        }

        // Import custom products and categories if available
        if (importedData.products && Array.isArray(importedData.products)) {
          try {
            // Filter and validate custom products
            const customProductsToImport = importedData.products.filter(p => p.isCustom);

            // Validate each product has required fields
            const validatedProducts = customProductsToImport.map(product => ({
              // Core product information
              id: product.id || `custom-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
              name: product.name || 'Unnamed Product',
              category: product.category || 'Uncategorized',
              subcategory: product.subcategory || 'General',

              // Product details with complete data preservation
              price: typeof product.price === 'number' ? product.price : parseFloat(product.price) || 0,
              size: product.size || product.diameter || '',
              diameter: product.diameter || product.size || '',
              material: product.material || '',
              image: product.image || '',

              // Product metadata
              isCustom: true,
              createdAt: product.createdAt || new Date().toISOString(),
              updatedAt: product.updatedAt || new Date().toISOString(),

              // Additional fields preservation
              description: product.description || '',
              brand: product.brand || '',
              model: product.model || '',
              weight: product.weight || '',
              color: product.color || '',

              // Preserve any other custom fields
              ...Object.keys(product).reduce((acc, key) => {
                if (!['id', 'name', 'category', 'subcategory', 'price', 'size', 'diameter', 'material', 'image', 'isCustom', 'createdAt', 'updatedAt', 'description', 'brand', 'model', 'weight', 'color'].includes(key)) {
                  acc[key] = product[key];
                }
                return acc;
              }, {})
            }));

            // Use the importSettings function to handle custom products/categories
            const settingsImportResult = importSettings({
              customProducts: validatedProducts,
              customCategories: importedData.customCategories || importedData.categories || []
            });

            if (settingsImportResult.success) {
              console.log('Custom products and categories imported successfully:', {
                productsImported: validatedProducts.length,
                categoriesImported: (importedData.customCategories || importedData.categories || []).length
              });
            }
          } catch (importError) {
            console.warn('Failed to import custom products/categories:', importError);
          }
        }

        // Trigger category changes callback to refresh UI
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        // Force a complete UI refresh by updating the category refresh key
        // This ensures all components re-render with the new data
        setTimeout(() => {
          if (onCategoryChanges) {
            onCategoryChanges();
          }
        }, 100);

        // Prepare success message with import details
        const metadata = importedData.metadata || {};
        let successMessage = 'Data imported successfully';

        const details = [];
        if (importedData.canvasProducts?.length) {
          details.push(`${importedData.canvasProducts.length} canvas items`);
        }
        if (importedData.connections?.length) {
          details.push(`${importedData.connections.length} connections`);
        }
        if (importedData.products?.length) {
          details.push(`${importedData.products.length} products`);
        }
        if (importedData.categories?.length) {
          details.push(`${importedData.categories.length} categories`);
        }

        if (details.length > 0) {
          successMessage += ` (${details.join(', ')})`;
        }

        setDataOperationStatus({
          type: 'success',
          message: successMessage
        });

        console.log('Import completed successfully:', {
          fileSize: result.fileSize,
          productCount: result.productCount,
          canvasProducts: importedData.canvasProducts?.length || 0,
          connections: importedData.connections?.length || 0,
          metadata: metadata,
          dataStructure: Object.keys(importedData)
        });

        // Close the modal after successful import to let user see the changes
        setTimeout(() => {
          onClose();
        }, 2000);

      } else {
        throw new Error(result.message || 'Import failed - no data received');
      }
    } catch (error) {
      console.error('Import error:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Import failed: ' + error.message
      });
    } finally {
      setIsDataLoading(false);
      event.target.value = '';
    }
  };

  const handleRestoreBackup = async (backupKey) => {
    setIsDataLoading(true);
    try {
      const result = restoreFromBackup(backupKey);
      if (result.success && result.data) {
        // Update application state with restored data
        if (result.data.canvasProducts && Array.isArray(result.data.canvasProducts) && onLoadProject) {
          onLoadProject({
            products: result.data.canvasProducts,
            connections: result.data.connections || [],
            selectedProducts: result.data.selectedProducts || []
          });
        }

        // Save the restored data to localStorage for persistence
        try {
          const saveResult = await saveApplicationData(result.data);
          if (saveResult.success) {
            console.log('Restored data saved to localStorage successfully');
          }
        } catch (saveError) {
          console.warn('Error saving restored data to localStorage:', saveError);
        }

        // Trigger UI refresh
        if (onCategoryChanges) {
          onCategoryChanges();
        }

        setDataOperationStatus({
          type: 'success',
          message: 'Backup restored successfully'
        });

        // Refresh backup list after restore
        setBackups(getBackups());

        // Close the modal after successful restore to let user see the changes
        setTimeout(() => {
          onClose();
        }, 2000);
      } else {
        throw new Error(result.message || 'Failed to restore backup');
      }
    } catch (error) {
      console.error('Restore backup error:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Restore failed: ' + error.message
      });
    } finally {
      setIsDataLoading(false);
    }
  };

  const confirmDeleteBackup = (backup) => {
    setBackupToDelete(backup);
    setShowDeleteBackupConfirm(true);
  };

  const handleDeleteBackup = async () => {
    if (!backupToDelete) return;

    setIsDataLoading(true);
    try {
      const result = deleteBackup(backupToDelete.key);
      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: 'Backup deleted successfully'
        });
        // Refresh backup list after deletion
        setBackups(getBackups());
      } else {
        throw new Error(result.message);
      }
    } catch (error) {
      setDataOperationStatus({
        type: 'error',
        message: 'Delete failed: ' + error.message
      });
    } finally {
      setIsDataLoading(false);
      setShowDeleteBackupConfirm(false);
      setBackupToDelete(null);
    }
  };

  const cancelDeleteBackup = () => {
    setShowDeleteBackupConfirm(false);
    setBackupToDelete(null);
  };

  // Bulk delete all backups functionality
  const confirmDeleteAllBackups = () => {
    if (backups.length === 0) {
      setDataOperationStatus({
        type: 'info',
        message: 'No backups available to delete'
      });
      return;
    }
    setShowDeleteAllBackupsConfirm(true);
  };

  const handleDeleteAllBackups = async () => {
    if (backups.length === 0) return;

    setIsDeletingAllBackups(true);
    try {
      const backupKeys = backups.map(backup => backup.key);
      const result = deleteMultipleBackups(backupKeys);

      if (result.success) {
        setDataOperationStatus({
          type: 'success',
          message: `All ${result.deletedCount} backup(s) deleted successfully`
        });
        // Refresh backup list after deletion
        setBackups(getBackups());
      } else {
        setDataOperationStatus({
          type: 'error',
          message: `Failed to delete some backups: ${result.errors.join(', ')}`
        });
        // Still refresh the list to show what was actually deleted
        setBackups(getBackups());
      }
    } catch (error) {
      console.error('Error deleting all backups:', error);
      setDataOperationStatus({
        type: 'error',
        message: 'Delete all backups failed: ' + error.message
      });
    } finally {
      setIsDeletingAllBackups(false);
      setShowDeleteAllBackupsConfirm(false);
    }
  };

  const cancelDeleteAllBackups = () => {
    setShowDeleteAllBackupsConfirm(false);
  };



  const toggleAutoSave = async () => {
    const newSettings = {
      autoSave: !autoSaveEnabled,
      autoLoad: true,
      backupInterval: 300000,
      maxBackups: 10
    };

    const result = saveDataSettings(newSettings);
    if (result.success) {
      setDataOperationStatus({
        type: 'success',
        message: `Auto-save ${newSettings.autoSave ? 'enabled' : 'disabled'}`
      });
    }
  };

  const formatDate = (dateString) => {
    if (!dateString) return 'Never';
    return new Date(dateString).toLocaleString();
  };

  // Load backups when database tab is opened
  React.useEffect(() => {
    if (activeTab === 'database') {
      setBackups(getBackups());
      // Clear any previous status messages when switching to database tab
      setDataOperationStatus(null);
    }
  }, [activeTab, getBackups]);

  // Clear status messages after 5 seconds
  React.useEffect(() => {
    if (dataOperationStatus) {
      const timer = setTimeout(() => {
        setDataOperationStatus(null);
      }, 5000);
      return () => clearTimeout(timer);
    }
  }, [dataOperationStatus]);
  // Render Settings Tab
  const renderSettingsTab = () => {
    return (
      <div className="p-4 flex flex-col h-full">
        <h3 className="font-bold mb-6 text-gray-900 text-xl">{t('Application Settings')}</h3>

        {/* Language Selection */}
        <div className="mb-6">
          <label className="block font-medium mb-3 text-gray-900">{t('Language')}</label>
          <select
            className="w-full p-3 border border-gray-300 rounded bg-white text-gray-900 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            value={language}
            onChange={(e) => setLanguage(e.target.value)}
          >
            <option value="en">English</option>
            <option value="fr">Français</option>
            <option value="es">Español</option>
          </select>
        </div>



        {/* Product Template Builder Section */}
        <div className="mb-6">
          <h4 className="font-bold mb-3 text-gray-900">{t('Product Template Builder')}</h4>
          <div className="flex justify-start">
            <button
              onClick={() => setShowStandaloneTemplateBuilder(true)}
              className="px-6 py-3 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 flex items-center transition-colors shadow-md font-medium"
              title={t('templateBuilderDescription')}
            >
              <Plus size={20} className="mr-2" />
              {t('buildTemplate')}
            </button>
          </div>
        </div>


      </div>
    );
  };



  // If modal is not open, don't render anything
  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl h-[80vh] flex flex-col overflow-hidden border-2 border-gray-200">
        {/* Modal header with tabs */}
        <div className="flex items-center justify-between p-4 bg-gray-50 border-b">
          <div className="flex items-center">
            <Database className="mr-2 text-blue-600" size={20} />
            <h2 className="text-xl font-bold text-gray-800">{t('settings')}</h2>
          </div>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-red-100 text-red-600 transition-colors duration-200"
            title={t('Close')}
          >
            <X size={20} />
          </button>
        </div>

        {/* Tabs navigation */}
        <div className="flex border-b bg-gray-50">
          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'categories'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('categories')}
          >
            <div className="flex items-center">
              <Tag size={16} className="mr-2" />
              {t('Categories')}
            </div>
          </button>
          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'products'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('products')}
          >
            <div className="flex items-center">
              <Package size={16} className="mr-2" />
              {t('Products')}
            </div>
          </button>

          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'settings'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('settings')}
          >
            <div className="flex items-center">
              <Database size={16} className="mr-2" />
              {t('Settings')}
            </div>
          </button>

          <button
            className={`px-6 py-3 font-medium transition-colors duration-200 ${
              activeTab === 'database'
                ? 'text-blue-600 border-b-2 border-blue-600 bg-white'
                : 'text-gray-600 hover:bg-gray-100'
            }`}
            onClick={() => setActiveTab('database')}
          >
            <div className="flex items-center">
              <Database size={16} className="mr-2" />
              {t('Database')}
            </div>
          </button>

        </div>

        {/* Tab content */}
        <div className="flex-1 overflow-hidden">
          {renderTabContent()}
        </div>

        {/* Bottom action bar */}
        <div className="p-4 border-t bg-gray-50 flex justify-end">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors duration-200 flex items-center"
          >
            <X size={16} className="mr-2" />
            {t('Exit')}
          </button>
        </div>

        {/* Standalone Product Template Builder */}
        <ProductTemplateBuilder
          isOpen={showStandaloneTemplateBuilder}
          onClose={() => setShowStandaloneTemplateBuilder(false)}
        />

        {/* Database Management Panel */}
        <DatabaseManagementPanel
          isOpen={showDatabasePanel}
          onClose={() => setShowDatabasePanel(false)}
        />

        {/* Confirmation dialog */}
        {confirmationDialogOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-md mx-4">
              <h3 className="font-bold mb-4 text-gray-900 text-lg">{t('Confirm Action')}</h3>
              <p className="mb-6 text-gray-700">{t('Are you sure you want to proceed? This action cannot be undone.')}</p>
              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setConfirmationDialogOpen(false)}
                  className="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400 transition-colors"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={confirmationAction}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors"
                >
                  {t('Confirm')}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Deletion confirmation modal */}
        {showDeleteConfirm && deleteTarget && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-lg mx-4">
              <div className="flex items-center mb-4">
                <AlertCircle className="text-red-600 mr-3" size={24} />
                <h3 className="font-bold text-gray-900 text-lg">
                  {t('Delete')} {deleteType === 'category' ? t('Category') : t('Subcategory')}
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-900 mb-3 font-medium">
                  {deleteType === 'category'
                    ? t('Are you sure you want to permanently delete the category')
                    : t('Are you sure you want to permanently delete the subcategory')
                  } <strong className="text-black">"{deleteTarget.name}"</strong>?
                </p>

                {deleteTarget.productCount > 0 && (
                  <div className="bg-yellow-50 border border-yellow-200 rounded p-3 mb-4">
                    <p className="text-yellow-800 font-medium">
                      ⚠️ {t('Warning')}: {deleteTarget.productCount} {deleteTarget.productCount === 1 ? t('product') : t('products')}
                      {deleteType === 'category'
                        ? ` ${t('in this category will be affected')}`
                        : ` ${t('in this subcategory will be affected')}`
                      }
                    </p>
                  </div>
                )}

                <div className="space-y-3">
                  <p className="font-medium text-black">{t('What should happen to the affected products?')}</p>

                  <div className="space-y-2">
                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="productHandling"
                        value="orphan"
                        checked={productHandlingOption === 'orphan'}
                        onChange={(e) => setProductHandlingOption(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-gray-900 font-medium">
                        {deleteType === 'category'
                          ? t('Move products to "Uncategorized"')
                          : t('Move products to "Other" subcategory')
                        }
                      </span>
                    </label>

                    <label className="flex items-center">
                      <input
                        type="radio"
                        name="productHandling"
                        value="delete"
                        checked={productHandlingOption === 'delete'}
                        onChange={(e) => setProductHandlingOption(e.target.value)}
                        className="mr-2"
                      />
                      <span className="text-sm text-red-600 font-medium">
                        {t('Delete all products permanently')}
                      </span>
                    </label>

                    {deleteTarget.productCount > 0 && (
                      <label className="flex items-center">
                        <input
                          type="radio"
                          name="productHandling"
                          value="reassign"
                          checked={productHandlingOption === 'reassign'}
                          onChange={(e) => setProductHandlingOption(e.target.value)}
                          className="mr-2"
                        />
                        <span className="text-sm text-gray-900 font-medium">
                          {deleteType === 'category'
                            ? t('Reassign products to another category')
                            : t('Reassign products to another subcategory')
                          }
                        </span>
                      </label>
                    )}
                  </div>

                  {productHandlingOption === 'reassign' && (
                    <div className="mt-3 p-3 bg-blue-50 border border-blue-200 rounded">
                      {deleteType === 'category' ? (
                        <div>
                          <label className="block text-sm font-medium text-black mb-1">
                            {t('Select target category')}:
                          </label>
                          <select
                            value={reassignTarget.category}
                            onChange={(e) => setReassignTarget(prev => ({
                              ...prev,
                              category: e.target.value,
                              subcategory: ''
                            }))}
                            className="w-full p-2 border border-gray-300 rounded text-sm text-gray-900"
                          >
                            <option value="">{t('Select category')}</option>
                            {getAvailableCategories(deleteTarget.name).map(cat => (
                              <option key={cat} value={cat}>{cat}</option>
                            ))}
                          </select>
                          {reassignTarget.category && (
                            <div className="mt-2">
                              <label className="block text-sm font-medium text-black mb-1">
                                {t('Select target subcategory')}:
                              </label>
                              <select
                                value={reassignTarget.subcategory}
                                onChange={(e) => setReassignTarget(prev => ({
                                  ...prev,
                                  subcategory: e.target.value
                                }))}
                                className="w-full p-2 border border-gray-300 rounded text-sm text-gray-900"
                              >
                                <option value="">{t('Select subcategory')}</option>
                                {getAvailableSubcategories(reassignTarget.category).map(sub => (
                                  <option key={sub} value={sub}>{sub}</option>
                                ))}
                              </select>
                            </div>
                          )}
                        </div>
                      ) : (
                        <div>
                          <label className="block text-sm font-medium text-black mb-1">
                            {t('Select target subcategory')}:
                          </label>
                          <select
                            value={reassignTarget.subcategory}
                            onChange={(e) => setReassignTarget(prev => ({
                              ...prev,
                              subcategory: e.target.value
                            }))}
                            className="w-full p-2 border border-gray-300 rounded text-sm text-gray-900"
                          >
                            <option value="">{t('Select subcategory')}</option>
                            {getAvailableSubcategories(deleteTarget.category, deleteTarget.name).map(sub => (
                              <option key={sub} value={sub}>{sub}</option>
                            ))}
                          </select>
                        </div>
                      )}
                    </div>
                  )}
                </div>

                {deleteError && (
                  <div className="mt-3 p-3 bg-red-50 border border-red-200 rounded">
                    <p className="text-red-800 text-sm font-medium">{deleteError}</p>
                  </div>
                )}
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={handleCancelDeletion}
                  disabled={isDeleting}
                  className="px-4 py-2 bg-gray-300 text-gray-900 rounded hover:bg-gray-400 transition-colors disabled:opacity-50 font-medium"
                >
                  {t('Cancel')}
                </button>
                <button
                  onClick={handleConfirmDeletion}
                  disabled={isDeleting || (productHandlingOption === 'reassign' && (
                    deleteType === 'category'
                      ? !reassignTarget.category
                      : !reassignTarget.subcategory
                  ))}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium"
                >
                  {isDeleting ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      <span className="text-white">{t('Deleting...')}</span>
                    </>
                  ) : (
                    <>
                      <Trash2 size={16} className="mr-2" />
                      <span className="text-white">{t('Delete Permanently')}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Enhanced Clear All Data Dialog */}
        {showClearAllDialog && (
          <div className="fixed inset-0 z-[60] flex items-center justify-center bg-black bg-opacity-75">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-lg mx-4 border-2 border-red-200">
              <div className="flex items-center mb-4">
                <AlertTriangle className="text-red-600 mr-3" size={24} />
                <h3 className="font-bold text-gray-900 text-lg">
                  {t('clearAllDataTitle') || 'Clear All Application Data'}
                </h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-700 mb-4">
                  {t('clearAllDataDescription') || 'This action will permanently remove all your data and reset the application to its initial state.'}
                </p>



                <div className="bg-blue-50 border border-blue-200 rounded p-3 mb-4">
                  <p className="text-blue-800 text-sm">
                    💡 {t('undoAvailable') || 'You can restore default data using the "Restore Default Data" button'}
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={() => setShowClearAllDialog(false)}
                  disabled={isClearing}
                  className="px-4 py-2 bg-gray-300 text-gray-900 rounded hover:bg-gray-400 transition-colors disabled:opacity-50 font-medium"
                >
                  {t('Cancel')}
                </button>

                <button
                  onClick={handleClearAllData}
                  disabled={isClearing}
                  className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium"
                >
                  {isClearing ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      <span className="text-white">{t('Clearing...')}</span>
                    </>
                  ) : (
                    <>
                      <Trash2 size={16} className="mr-2" />
                      <span className="text-white">{t('clearAllData') || 'Clear All Data'}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Edit Product Modal */}
        {showEditProductModal && editingProduct && (
          <div className="fixed inset-0 z-[70] flex items-center justify-center bg-black bg-opacity-75">
            <div className="bg-white rounded-lg shadow-xl p-6 max-w-2xl mx-4 w-full max-h-[90vh] overflow-y-auto">
              <div className="flex items-center mb-6">
                <Edit className="text-blue-600 mr-3" size={24} />
                <h3 className="font-bold text-black text-xl">
                  {t('Edit Product') || 'Edit Product'}
                </h3>
              </div>

              <div className="space-y-4">
                {/* General Error Message */}
                {editProductErrors.general && (
                  <div className="p-3 rounded bg-red-50 border border-red-200 text-red-800">
                    <p className="text-sm font-medium">{editProductErrors.general}</p>
                  </div>
                )}

                {/* Product Name */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    {t('Product Name') || 'Product Name'} *
                  </label>
                  <input
                    type="text"
                    value={editProductForm.name}
                    onChange={(e) => handleEditProductFormChange('name', e.target.value)}
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-500 ${
                      editProductErrors.name ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder={t('Enter product name') || 'Enter product name'}
                  />
                  {editProductErrors.name && (
                    <p className="text-red-600 text-sm mt-1">{editProductErrors.name}</p>
                  )}
                </div>

                {/* Category */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    {t('Category') || 'Category'} *
                  </label>
                  <select
                    value={editProductForm.category}
                    onChange={(e) => {
                      handleEditProductFormChange('category', e.target.value);
                      handleEditProductFormChange('subcategory', ''); // Reset subcategory
                    }}
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black ${
                      editProductErrors.category ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                  >
                    <option value="" className="text-gray-500">{t('Select category') || 'Select category'}</option>
                    {getAvailableCategories().map(category => (
                      <option key={category.name} value={category.name} className="text-black">
                        {category.name}
                      </option>
                    ))}
                  </select>
                  {editProductErrors.category && (
                    <p className="text-red-600 text-sm mt-1">{editProductErrors.category}</p>
                  )}
                </div>

                {/* Subcategory */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    {t('Subcategory') || 'Subcategory'} *
                  </label>
                  <select
                    value={editProductForm.subcategory}
                    onChange={(e) => handleEditProductFormChange('subcategory', e.target.value)}
                    disabled={!editProductForm.category}
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black disabled:bg-gray-100 disabled:cursor-not-allowed disabled:text-gray-500 ${
                      editProductErrors.subcategory ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                  >
                    <option value="" className="text-gray-500">{t('Select subcategory') || 'Select subcategory'}</option>
                    {editProductForm.category && getAvailableSubcategories(editProductForm.category).map(subcategory => (
                      <option key={subcategory} value={subcategory} className="text-black">
                        {subcategory}
                      </option>
                    ))}
                  </select>
                  {editProductErrors.subcategory && (
                    <p className="text-red-600 text-sm mt-1">{editProductErrors.subcategory}</p>
                  )}
                </div>

                {/* Price */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    {t('Price') || 'Price'}
                  </label>
                  <input
                    type="number"
                    step="0.01"
                    min="0"
                    value={editProductForm.price}
                    onChange={(e) => handleEditProductFormChange('price', e.target.value)}
                    className={`w-full p-3 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-500 ${
                      editProductErrors.price ? 'border-red-300 bg-red-50' : 'border-gray-300'
                    }`}
                    placeholder="0.00"
                  />
                  {editProductErrors.price && (
                    <p className="text-red-600 text-sm mt-1">{editProductErrors.price}</p>
                  )}
                </div>

                {/* Size/Diameter */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    {t('Size/Diameter') || 'Size/Diameter'}
                  </label>
                  <input
                    type="text"
                    value={editProductForm.size}
                    onChange={(e) => handleEditProductFormChange('size', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-500"
                    placeholder={t('e.g., 1/2", 3/4", 1"') || 'e.g., 1/2", 3/4", 1"'}
                  />
                </div>

                {/* Material */}
                <div>
                  <label className="block text-sm font-medium text-black mb-2">
                    {t('Material') || 'Material'}
                  </label>
                  <input
                    type="text"
                    value={editProductForm.material}
                    onChange={(e) => handleEditProductFormChange('material', e.target.value)}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-500"
                    placeholder={t('e.g., Copper, PVC, Steel') || 'e.g., Copper, PVC, Steel'}
                  />
                </div>

                {/* Product Image */}
                <div>
                  <label className="block text-sm font-medium text-black mb-3">
                    {t('Product Image') || 'Product Image'}
                  </label>

                  {/* Image Upload Error */}
                  {imageUploadError && (
                    <div className="mb-3 p-3 rounded bg-red-50 border border-red-200 text-red-800">
                      <p className="text-sm font-medium">{imageUploadError}</p>
                    </div>
                  )}

                  {/* Current Image Preview */}
                  {imagePreview && (
                    <div className="mb-4 p-4 border border-gray-200 rounded-lg bg-gray-50">
                      <div className="flex items-start space-x-4">
                        <div className="flex-shrink-0">
                          <img
                            src={imagePreview}
                            alt="Product preview"
                            className="w-20 h-20 object-contain border border-gray-300 rounded bg-white"
                            onError={(e) => {
                              e.target.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODAiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0yNCAzMkw0MCA0OEw1NiAzMkw2NCA0MEw2NCA1NkgxNlY0MEwyNCAzMloiIGZpbGw9IiM5Q0EzQUYiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iNCIgZmlsbD0iIzlDQTNBRiIvPgo8L3N2Zz4K';
                            }}
                          />
                        </div>
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-gray-900 mb-1">
                            {t('Current Image') || 'Current Image'}
                          </p>
                          <p className="text-xs text-gray-500 break-all">
                            {imagePreview.startsWith('data:')
                              ? t('Uploaded file') || 'Uploaded file'
                              : imagePreview
                            }
                          </p>
                        </div>
                        <button
                          type="button"
                          onClick={handleRemoveImage}
                          className="flex-shrink-0 p-1 text-red-600 hover:text-red-800 hover:bg-red-100 rounded"
                          title={t('Remove image') || 'Remove image'}
                        >
                          <X size={16} />
                        </button>
                      </div>
                    </div>
                  )}

                  {/* Upload Options */}
                  <div className="space-y-4">
                    {/* File Upload */}
                    <div>
                      <label className={`inline-flex items-center px-4 py-3 rounded-lg cursor-pointer transition-all duration-200 border-2 border-dashed font-medium ${
                        isProcessingImage
                          ? 'bg-gray-100 text-gray-400 cursor-not-allowed border-gray-300'
                          : 'bg-blue-50 text-blue-700 hover:bg-blue-100 border-blue-300 hover:border-blue-400'
                      }`}>
                        <Upload size={16} className="mr-2" />
                        {isProcessingImage
                          ? (t('Processing...') || 'Processing...')
                          : (t('Upload New Image') || 'Upload New Image')
                        }
                        <input
                          type="file"
                          accept="image/png,image/jpeg,image/jpg,image/webp"
                          onChange={handleImageUpload}
                          className="hidden"
                          disabled={isProcessingImage}
                        />
                      </label>
                      <p className="text-xs text-gray-500 mt-2">
                        {t('Supported formats: PNG, JPG, JPEG, WebP (max 2MB)') || 'Supported formats: PNG, JPG, JPEG, WebP (max 2MB)'}
                      </p>
                    </div>

                    {/* URL Input */}
                    <div>
                      <label className="block text-xs font-medium text-gray-700 mb-2">
                        {t('Or enter image URL') || 'Or enter image URL'}
                      </label>
                      <input
                        type="url"
                        value={editProductForm.image.startsWith('data:') ? '' : editProductForm.image}
                        onChange={(e) => handleImageUrlChange(e.target.value)}
                        className="w-full p-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 text-black placeholder-gray-500 text-sm"
                        placeholder="https://example.com/image.jpg"
                        disabled={isProcessingImage}
                      />
                    </div>
                  </div>
                </div>
              </div>

              <div className="flex justify-end space-x-3 mt-6 pt-4 border-t">
                <button
                  onClick={handleCancelEditProduct}
                  disabled={isUpdatingProduct}
                  className="px-6 py-2 bg-gray-300 text-gray-900 rounded hover:bg-gray-400 transition-colors disabled:opacity-50 font-medium"
                >
                  {t('Cancel')}
                </button>

                <button
                  onClick={handleSaveEditedProduct}
                  disabled={isUpdatingProduct}
                  className="px-6 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center font-medium"
                >
                  {isUpdatingProduct ? (
                    <>
                      <RefreshCw size={16} className="mr-2 animate-spin" />
                      <span>{t('Updating...') || 'Updating...'}</span>
                    </>
                  ) : (
                    <>
                      <Save size={16} className="mr-2" />
                      <span>{t('Save Changes') || 'Save Changes'}</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Backup Deletion Confirmation Dialog */}
        {showDeleteBackupConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[80]">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4">
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
                <h3 className="text-lg font-semibold text-gray-900">Delete Backup</h3>
              </div>

              <p className="text-gray-700 mb-6">
                Are you sure you want to delete this backup? This action cannot be undone.
              </p>

              {backupToDelete && (
                <div className="bg-gray-50 rounded p-3 mb-6">
                  <div className="text-sm font-medium text-gray-900">
                    {formatDate(backupToDelete.timestamp)}
                  </div>
                  <div className="text-xs text-gray-500">
                    Size: {Math.round(backupToDelete.size / 1024)} KB
                  </div>
                </div>
              )}

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteBackup}
                  disabled={isDataLoading}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteBackup}
                  disabled={isDataLoading}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded transition-colors flex items-center space-x-2"
                >
                  {isDataLoading && <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />}
                  <span>Delete</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Delete All Backups Confirmation Dialog */}
        {showDeleteAllBackupsConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[80]">
            <div className="bg-white rounded-lg p-6 max-w-md w-full mx-4 border-2 border-red-200">
              <div className="flex items-center space-x-3 mb-4">
                <AlertTriangle className="w-6 h-6 text-red-600" />
                <h3 className="text-lg font-semibold text-gray-900">Delete All Backups</h3>
              </div>

              <div className="mb-6">
                <p className="text-gray-700 mb-4">
                  Are you sure you want to delete <strong>all {backups.length} backup(s)</strong>?
                  This action cannot be undone and you will lose all backup history.
                </p>

                <div className="bg-red-50 border border-red-200 rounded p-3">
                  <p className="text-red-800 text-sm font-medium">
                    ⚠️ Warning: This will permanently delete all backup files. You will not be able to restore any previous versions of your work.
                  </p>
                </div>
              </div>

              <div className="flex justify-end space-x-3">
                <button
                  onClick={cancelDeleteAllBackups}
                  disabled={isDeletingAllBackups}
                  className="px-4 py-2 text-gray-700 bg-gray-200 hover:bg-gray-300 disabled:opacity-50 rounded transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteAllBackups}
                  disabled={isDeletingAllBackups}
                  className="px-4 py-2 bg-red-600 hover:bg-red-700 disabled:opacity-50 text-white rounded transition-colors flex items-center space-x-2"
                >
                  {isDeletingAllBackups ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
                      <span>Deleting...</span>
                    </>
                  ) : (
                    <>
                      <Trash2 className="w-4 h-4" />
                      <span>Delete All {backups.length} Backups</span>
                    </>
                  )}
                </button>
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  );
};

export default SettingsModal;
