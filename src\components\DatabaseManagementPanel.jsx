/**
 * Database Management Panel for PlombDesign
 * Provides UI for SQLite database operations and monitoring
 */

import React, { useState, useEffect } from 'react';
import { Database, Upload, Download, Trash2, BarChart3, RefreshCw, AlertCircle, CheckCircle, X } from 'lucide-react';
import { enhancedDataStorage } from '../utils/enhancedDataStorage';
import { sqliteManager } from '../utils/sqliteManager';
import { unifiedDataOps } from '../utils/unifiedDataOperations';

const DatabaseManagementPanel = ({ isOpen, onClose }) => {
  const [stats, setStats] = useState(null);
  const [isLoading, setIsLoading] = useState(false);
  const [message, setMessage] = useState(null);
  const [operationProgress, setOperationProgress] = useState(null);
  const [currentOperation, setCurrentOperation] = useState(null);

  // Load database statistics
  useEffect(() => {
    if (isOpen) {
      loadStats();
      setupUnifiedOperations();
    }
  }, [isOpen]);

  const setupUnifiedOperations = () => {
    // Set up callbacks for unified operations
    unifiedDataOps.setCallbacks(
      (progress) => setOperationProgress(progress),
      (status) => showMessage(status.message, status.type)
    );
  };

  const loadStats = async () => {
    try {
      setIsLoading(true);
      const result = await enhancedDataStorage.getStorageStats();
      if (result.success) {
        setStats(result.stats);
      }
    } catch (error) {
      console.error('Error loading stats:', error);
      showMessage('Error loading database statistics', 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const showMessage = (text, type = 'info') => {
    setMessage({ text, type });
    setTimeout(() => setMessage(null), 5000);
  };

  const handleInitializeDatabase = async () => {
    try {
      setIsLoading(true);
      showMessage('Initializing database...', 'info');
      
      const result = await enhancedDataStorage.initialize();
      
      if (result.success) {
        showMessage(result.message, 'success');
        await loadStats();
      } else {
        showMessage(result.message, 'error');
      }
    } catch (error) {
      showMessage(`Initialization failed: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  const handleExportDatabase = async () => {
    try {
      setIsLoading(true);
      setCurrentOperation('export');

      const result = await unifiedDataOps.exportData({
        filename: `plombdesign-database-${new Date().toISOString().split('T')[0]}.json`,
        enableChunking: true
      });

      if (result.success) {
        // Refresh stats after export
        await loadStats();
      }
    } catch (error) {
      // Error handling is done by unified operations
      console.error('Export error:', error);
    } finally {
      setIsLoading(false);
      setCurrentOperation(null);
      setOperationProgress(null);
    }
  };

  const handleImportFile = async (event) => {
    const file = event.target.files[0];
    if (!file) return;

    try {
      setIsLoading(true);
      setCurrentOperation('import');

      const result = await unifiedDataOps.importData(file, {
        enableChunking: true,
        validateData: true
      });

      if (result.success) {
        // Refresh stats after import
        await loadStats();
      }
    } catch (error) {
      // Error handling is done by unified operations
      console.error('Import error:', error);
    } finally {
      setIsLoading(false);
      setCurrentOperation(null);
      setOperationProgress(null);
      event.target.value = ''; // Reset file input
    }
  };

  const handleClearDatabase = async () => {
    if (!window.confirm('Are you sure you want to clear all database data? This action cannot be undone.')) {
      return;
    }

    try {
      setIsLoading(true);
      showMessage('Clearing database...', 'info');
      
      const result = await sqliteManager.clearAllData();
      
      if (result.success) {
        showMessage('Database cleared successfully', 'success');
        await loadStats();
      } else {
        showMessage(result.message, 'error');
      }
    } catch (error) {
      showMessage(`Clear failed: ${error.message}`, 'error');
    } finally {
      setIsLoading(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center space-x-3">
            <Database className="w-6 h-6 text-blue-600" />
            <h2 className="text-xl font-semibold">Database Management</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600"
          >
            ×
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {/* Message Display */}
          {message && (
            <div className={`mb-4 p-3 rounded-lg flex items-center space-x-2 ${
              message.type === 'error' ? 'bg-red-100 text-red-700' :
              message.type === 'success' ? 'bg-green-100 text-green-700' :
              message.type === 'warning' ? 'bg-yellow-100 text-yellow-700' :
              'bg-blue-100 text-blue-700'
            }`}>
              {message.type === 'error' && <AlertCircle className="w-4 h-4" />}
              {message.type === 'success' && <CheckCircle className="w-4 h-4" />}
              <span>{message.text}</span>
            </div>
          )}

          {/* Operation Progress */}
          {operationProgress && (
            <div className="mb-4 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">
                  {currentOperation === 'export' ? 'Exporting Data' :
                   currentOperation === 'import' ? 'Importing Data' :
                   'Processing'}
                  {operationProgress.stage && ` - ${operationProgress.stage.replace('-', ' ')}`}
                </span>
                <div className="flex items-center space-x-2">
                  {operationProgress.processed !== undefined && operationProgress.total !== undefined && (
                    <span className="text-sm text-gray-600">
                      {operationProgress.processed.toLocaleString()} / {operationProgress.total.toLocaleString()}
                    </span>
                  )}
                  {operationProgress.successful !== undefined && (
                    <span className="text-xs text-green-600">
                      ✓ {operationProgress.successful}
                    </span>
                  )}
                  {operationProgress.failed !== undefined && operationProgress.failed > 0 && (
                    <span className="text-xs text-red-600">
                      ✗ {operationProgress.failed}
                    </span>
                  )}
                </div>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${operationProgress.percentage || 0}%` }}
                />
              </div>
              <div className="flex justify-between items-center mt-1">
                <div className="text-xs text-gray-600">
                  {operationProgress.percentage || 0}% complete
                </div>
                {currentOperation && (
                  <button
                    onClick={() => unifiedDataOps.cancelOperation()}
                    className="text-xs text-red-600 hover:text-red-800 flex items-center space-x-1"
                  >
                    <X className="w-3 h-3" />
                    <span>Cancel</span>
                  </button>
                )}
              </div>
            </div>
          )}

          {/* Database Status */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3 flex items-center">
                <BarChart3 className="w-4 h-4 mr-2" />
                Database Status
              </h3>
              {isLoading ? (
                <div className="flex items-center space-x-2">
                  <RefreshCw className="w-4 h-4 animate-spin" />
                  <span>Loading...</span>
                </div>
              ) : stats ? (
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>SQLite Enabled:</span>
                    <span className={stats.sqliteEnabled ? 'text-green-600' : 'text-red-600'}>
                      {stats.sqliteEnabled ? 'Yes' : 'No'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Migration Status:</span>
                    <span className={stats.migrationCompleted ? 'text-green-600' : 'text-yellow-600'}>
                      {stats.migrationCompleted ? 'Complete' : 'Pending'}
                    </span>
                  </div>
                  {stats.sqlite && (
                    <>
                      <div className="flex justify-between">
                        <span>Total Products:</span>
                        <span className="font-medium">{stats.sqlite.totalProducts}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Database Size:</span>
                        <span className="font-medium">{stats.sqlite.formattedSize}</span>
                      </div>
                    </>
                  )}
                </div>
              ) : (
                <div className="text-gray-500">No data available</div>
              )}
            </div>

            <div className="bg-gray-50 p-4 rounded-lg">
              <h3 className="font-semibold mb-3">Products by Category</h3>
              {stats?.sqlite?.productsByCategory ? (
                <div className="space-y-1 text-sm max-h-32 overflow-y-auto">
                  {stats.sqlite.productsByCategory.map((item, index) => (
                    <div key={index} className="flex justify-between">
                      <span className="truncate">{item.category}</span>
                      <span className="font-medium">{item.count}</span>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-gray-500">No categories found</div>
              )}
            </div>
          </div>

          {/* Action Buttons */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <button
              onClick={handleInitializeDatabase}
              disabled={isLoading}
              className="flex items-center justify-center space-x-2 px-4 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              <Database className="w-4 h-4" />
              <span>Initialize DB</span>
            </button>

            <label className="flex items-center justify-center space-x-2 px-4 py-3 bg-green-600 text-white rounded-lg hover:bg-green-700 cursor-pointer">
              <Upload className="w-4 h-4" />
              <span>Import Data</span>
              <input
                type="file"
                accept=".json"
                onChange={handleImportFile}
                className="hidden"
                disabled={isLoading}
              />
            </label>

            <button
              onClick={handleExportDatabase}
              disabled={isLoading}
              className="flex items-center justify-center space-x-2 px-4 py-3 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
            >
              <Download className="w-4 h-4" />
              <span>Export DB</span>
            </button>

            <button
              onClick={handleClearDatabase}
              disabled={isLoading}
              className="flex items-center justify-center space-x-2 px-4 py-3 bg-red-600 text-white rounded-lg hover:bg-red-700 disabled:opacity-50"
            >
              <Trash2 className="w-4 h-4" />
              <span>Clear All</span>
            </button>
          </div>

          {/* Recent Products */}
          {stats?.sqlite?.recentProducts && stats.sqlite.recentProducts.length > 0 && (
            <div className="mt-6">
              <h3 className="font-semibold mb-3">Recent Products</h3>
              <div className="bg-gray-50 rounded-lg p-4">
                <div className="space-y-2 text-sm max-h-40 overflow-y-auto">
                  {stats.sqlite.recentProducts.map((product, index) => (
                    <div key={index} className="flex justify-between items-center">
                      <div>
                        <span className="font-medium">{product.name}</span>
                        <span className="text-gray-500 ml-2">({product.category})</span>
                      </div>
                      <span className="text-xs text-gray-400">
                        {new Date(product.lastUpdated).toLocaleDateString()}
                      </span>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Refresh Button */}
          <div className="mt-6 flex justify-center">
            <button
              onClick={loadStats}
              disabled={isLoading}
              className="flex items-center space-x-2 px-4 py-2 text-gray-600 hover:text-gray-800 disabled:opacity-50"
            >
              <RefreshCw className={`w-4 h-4 ${isLoading ? 'animate-spin' : ''}`} />
              <span>Refresh Statistics</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DatabaseManagementPanel;
