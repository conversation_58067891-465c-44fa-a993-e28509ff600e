import React from 'react';
import { Ruler, Package } from 'lucide-react';
import { useLanguage } from '../contexts/LanguageContext';

const ProductDetails = ({ product, position }) => {
  const { t, tc } = useLanguage();

  if (!product) return null;

  // Extract positioning data
  const dropdownPosition = product.dropdownPosition || 'right';
  const productCenter = position.productCenter || { x: position.x + 50, y: position.y + 35 };

  // Calculate arrow position and direction based on simplified positioning
  const isRight = dropdownPosition === 'right';
  const isLeft = dropdownPosition === 'left';
  const isBelow = dropdownPosition === 'below';
  const isAbove = dropdownPosition === 'above';
  const isConstrained = dropdownPosition === 'center-constrained';

  // Calculate arrow offset based on positioning
  let arrowLeftOffset, arrowTopOffset;

  if (isRight || isLeft) {
    // For horizontal positioning (right/left), arrow points horizontally
    // Calculate vertical offset from dropdown top to product center (adjusted for new height)
    arrowTopOffset = Math.max(16, Math.min(250, productCenter.y - position.y));
  } else if (isBelow || isAbove) {
    // For vertical positioning (above/below), arrow points vertically
    // Calculate horizontal offset from dropdown left to product center (adjusted for new width)
    arrowLeftOffset = Math.max(16, Math.min(284, productCenter.x - position.x));
  }

  return (
    <div
      className="dropdown-menu"
      style={{
        left: position.x,
        top: position.y,
        transform: 'none' // No transform needed with the new positioning system
      }}
      onMouseEnter={(e) => {
        // Keep dropdown visible when hovering over it
        e.stopPropagation();
      }}
      onMouseLeave={(e) => {
        // Allow dropdown to be hidden when leaving it
        e.stopPropagation();
      }}
    >
      {/* Single Box Container - Product Image with Info Below - Zero gap design */}
      <div className="bg-dark-800 overflow-hidden rounded-lg border border-dark-600 shadow-xl">
        {/* Large Product Image */}
        <div className="w-full h-52 bg-gradient-to-br from-white to-gray-50 flex items-center justify-center overflow-hidden">
          <img
            src={product.image}
            alt={product.name}
            className="w-full h-full object-contain transition-transform duration-300 hover:scale-105"
            style={{
              transform: `rotate(${product.rotation || 0}deg)`,
              transformOrigin: 'center'
            }}
            onError={(e) => {
              e.target.style.display = 'none';
              e.target.nextSibling.style.display = 'flex';
            }}
          />
          <div
            className="w-full h-full bg-gradient-to-br from-gray-100 to-gray-200 flex items-center justify-center text-gray-500 hidden"
          >
            <div className="text-center">
              <div className="text-3xl mb-1">📦</div>
              <div className="text-xs font-medium">No Image</div>
            </div>
          </div>
        </div>

        {/* Product Information Section - All info under image */}
        <div className="p-4 space-y-4">
          {/* Product Name and Category */}
          <div className="text-center">
            <h3 className="text-lg font-bold text-white mb-2 break-words hyphens-auto product-name-full" style={{
              lineHeight: '1.4',
              wordWrap: 'break-word',
              overflowWrap: 'break-word',
              hyphens: 'auto',
              wordBreak: 'break-word'
            }}>{product.name}</h3>
            <p className="text-sm text-primary-400 font-medium">{tc(product.category)} • {product.subcategory}</p>
          </div>

          {/* Product Details - Clean layout under image */}
          <div className="space-y-2">
            {/* Price */}
            <div className="flex items-center justify-between py-1">
              <div className="flex items-center gap-2">
                <span className="text-sm text-dark-200 font-medium">{t('price')}</span>
              </div>
              <span className="text-lg font-bold text-green-400">{product.price}</span>
            </div>

            {/* Quantity */}
            <div className="flex items-center justify-between py-1">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-orange-400" />
                <span className="text-sm text-dark-200 font-medium">{t('quantity') || 'Quantity'}</span>
              </div>
              <span className="text-sm font-semibold text-white">{product.quantity || 1}</span>
            </div>

            {/* Diameter */}
            <div className="flex items-center justify-between py-1">
              <div className="flex items-center gap-2">
                <Ruler className="w-4 h-4 text-blue-400" />
                <span className="text-sm text-dark-200 font-medium">{t('diameter')}</span>
              </div>
              <span className="text-sm font-semibold text-white">{product.diameter}</span>
            </div>

            {/* Material */}
            <div className="flex items-center justify-between py-1">
              <div className="flex items-center gap-2">
                <Package className="w-4 h-4 text-orange-400" />
                <span className="text-sm text-dark-200 font-medium">{t('material')}</span>
              </div>
              <span className="text-sm font-semibold text-white">{product.material}</span>
            </div>
          </div>
        </div>
      </div>

      {/* Dynamic Arrow pointing to product */}
      {!isConstrained && (
        <>
          {isRight ? (
            // Arrow pointing left (when dropdown is to the right of product)
            <div
              className="absolute left-0 -translate-x-full"
              style={{ top: `${arrowTopOffset}px` }}
            >
              <div className="w-0 h-0 border-t-8 border-b-8 border-l-8 border-t-transparent border-b-transparent border-l-dark-800"></div>
            </div>
          ) : isLeft ? (
            // Arrow pointing right (when dropdown is to the left of product)
            <div
              className="absolute right-0 translate-x-full"
              style={{ top: `${arrowTopOffset}px` }}
            >
              <div className="w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-dark-800"></div>
            </div>
          ) : isBelow ? (
            // Arrow pointing up (when dropdown is below product)
            <div
              className="absolute top-0 -translate-y-full"
              style={{ left: `${arrowLeftOffset}px` }}
            >
              <div className="w-0 h-0 border-l-8 border-r-8 border-t-8 border-l-transparent border-r-transparent border-t-dark-800"></div>
            </div>
          ) : isAbove ? (
            // Arrow pointing down (when dropdown is above product)
            <div
              className="absolute bottom-0 translate-y-full"
              style={{ left: `${arrowLeftOffset}px` }}
            >
              <div className="w-0 h-0 border-l-8 border-r-8 border-b-8 border-l-transparent border-r-transparent border-b-dark-800"></div>
            </div>
          ) : null}
        </>
      )}
    </div>
  );
};

export default ProductDetails;
